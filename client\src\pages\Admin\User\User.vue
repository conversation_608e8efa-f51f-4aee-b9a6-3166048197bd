<template>
  <Page title="Data Pengguna">
    <template v-slot:toolbar>
      <v-btn size="small" @click="showRolesPage = true">+ Role</v-btn>
    </template>
    <Grid
      v-model:datagrid="users"
      dbref="Arch.User"
      style="height: calc(100vh - 120px)"
      class="dense"
      :columns="[
        {
          name: 'Username',
          value: 'Username',
          width: '120px',
          editable: {
            com: 'XInput',
          },
          filter: {
            type: 'search',
          },
        },
        {
          name: 'Password',
          value: 'Password',
          width: '120px',
          editable: {
            com: 'XInput',
            type: 'password',
          },
        },
        {
          name: '<PERSON><PERSON>',
          value: 'FullName',
          width: '180px',
          editable: {
            com: 'XInput',
          },
        },
        {
          name: 'Aktif ?',
          value: 'IsActive',
          width: '70px',
          editable: {
            com: 'Checkbox',
          },
        },
        {
          name: 'Role',
          class: 'plain',
          value: 'RolePositionName',
          editable: {
            com: 'Select',
            value: 'RolePositionID',
            text: 'RolePositionName',
            dbref: 'Arch.SelRolePosition',
          },
        },
        {
          name: 'Area',
          class: 'center',
          width: '50px',
          value: 'AreaAccess',
        },
      ]"
    >
      <template #row-IsActive="{ row }">
        <div center>
          <v-icon>
            {{
              row.IsActive ? 'mdi-checkbox-marked-outline' : 'mdi-crop-square'
            }}
          </v-icon>
        </div>
      </template>
      <template #row-Password="{ row }">
        <div center>*******</div>
      </template>
      <template #row-RolePositionName="{ row }">
        <v-btn
          text
          small
          color="primary"
          @click="ShowRoleAccess(row.RolePositionID)"
        >
          {{ row.RolePositionName }}
        </v-btn>
      </template>
      <template #row-AreaAccess="{ row }">
        <div center>
          <v-icon @click="ShowAreaAccess(row.UserID)">
            mdi-map-marker-path
          </v-icon>
        </div>
      </template>
    </Grid>
    <Modal
      id="modal-role-access"
      v-model:show="roleAccess"
      title="ROLE ACCESS"
      width="350px"
      @onSubmit="SubmitRoleAccess"
    >
      <RoleAccessPage v-model:roleId="roleId" :page-data="roleAccessData" />
    </Modal>
    <Modal
      id="modal-roles"
      v-model:show="showRolesPage"
      title="ROLES"
      @onSubmit="SubmitRoles"
    >
      <RolesPage v-model:page-data="rolesData" />
    </Modal>
    <Modal
      id="modal-roles"
      v-model:show="showAreaAccess"
      title="AREA AKSES"
      @onSubmit="SubmitAreaAccess"
    >
      <AreaAccessPage v-model:userId="userId" :page-data="areaAccessData" />
    </Modal>
  </Page>
</template>
<script>
import RoleAccessPage from './RoleAccess.vue'
import RolesPage from './Roles.vue'
import AreaAccessPage from './AreaAccess.vue'

export default {
  components: {
    RoleAccessPage,
    RolesPage,
    AreaAccessPage,
  },
  data: () => ({
    roleAccess: false,
    showRolesPage: false,
    showAreaAccess: false,
    roleAccessData: [],
    rolesData: [],
    areaAccessData: [],
    roleId: null,
    userId: null,
    users: null,
    forms: {},
  }),
  async mounted() {},
  methods: {
    ShowRoleAccess(roleId) {
      this.roleAccess = true
      this.roleId = roleId
    },
    ShowAreaAccess(userId) {
      this.showAreaAccess = true
      this.userId = userId
    },
    async SubmitRoles() {},
    async SubmitRoleAccess() {
      let ret = this.$api.call('Arch.SavRoleAccess', {
        RolePositionID: this.roleId,
        XmlRoleAccess: this.roleAccessData,
      })
      if (ret.success) this.roleAccess = false
    },
    async SubmitAreaAccess() {
      let ret = await this.$api.call('Arch.SavUserArea', {
        UserID: this.userId,
        Remarks: this.areaAccessData
          .filter((a) => a.AllowAccess)
          .map((a) => a.AreaID)
          .join('|'),
      })

      if (ret.success) this.showAreaAccess = false
    },
  },
}
</script>
<style lang="scss">
#modal-role-access {
  .ui-table {
    height: 500px !important;
  }
}
</style>
