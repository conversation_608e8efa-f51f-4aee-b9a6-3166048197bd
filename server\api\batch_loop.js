var db = require('../common/db')

const xmethods = {
  async loop(kd) {
    let kab = []

    // kab = await db.query(`SELECT Kabupaten, Kecamatan , Kelurahan , KodeDagri FROM arch_varea av 
    // -- WHERE Kabupaten LIKE 'PATI'
    // WHERE KodeDagri > 3303082013
    // ORDER BY 4;`)

    kab = await db.query(`SELECT AreaName Kabupaten, KodeDagri FROM arch_area aa 
    where ParentAreaID = 33;`)

    for (let i = 0; i < kab.length; i++) {
      // let query = `UPDATE prm_pbdt pp
      //           JOIN raw_bdtart rb 
      //           ON rb.IDBDT = pp.NoRef 
      //       SET pp.NoKK = rb.NoKK 
      //       WHERE pp.NoKK IS NULL
      //       AND pp.KodeWilayah = ${kab[i].KodeDagri}`

      let query = `update prm_pbdt pp  
      join raw_bdtart rb 
      on pp.NIK = rb.NIK 
      set pp.IDBDT = rb.IDBDT
      where pp.IDBDT IS NULL and pp.Kabupaten = '${kab[i].Kabupaten}'`


      // let query = `UPDATE prm_pbdt SET SkorBacklog = CASE BLG_GetSkor(TipeData, MampuSwadaya, VerStatsID, TanahLain, KepemilikanRumah, KepemilikanLahan, JmlKK, LebarTanah, PanjangTanah, Penghasilan, NoNPWP, Perkawinan) 
      // WHEN ',PB' THEN 1 
      // WHEN ',BP2BT' THEN 10
      // WHEN ',PB,BP2BT' THEN 11
      // ELSE NULL END
      // WHERE KepemilikanRumah > 1 AND KodeWilayah = ${kab[i].KodeDagri}`

      let ret = await db.query(query)
      if (ret.length && ret[0].length) {
        // console.log()
      }
    }
  },
}

async function main() {
  // await xmethods.export("test", true);
  await xmethods.loop('')
}
main()
