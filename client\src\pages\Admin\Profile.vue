<template>
  <Page title="Ganti Password">
    <div class="form-inline padding" style="background: white">
      <XInput
        label="Password Lama"
        type="password"
        v-model:value="forms.OldPassword"
      />
      <br />
      <XInput
        label="Password Baru"
        type="password"
        v-model:value="forms.NewPassword"
      />
      <XInput
        label="Ulangi Password"
        type="password"
        v-model:value="forms.RptPassword"
      />
      <v-btn @click="Save" color="primary">SIMPAN</v-btn>
    </div>
  </Page>
</template>
<script>
import crypto from 'crypto'
export default {
  data: () => ({
    forms: {},
  }),
  methods: {
    md5(val) {
      return crypto.createHash('md5').update(val).digest('hex')
    },
    async Save() {
      await this.$api.call('Arch_SavPassword', this.forms)
    },
  },
}
</script>
<style lang="scss"></style>
