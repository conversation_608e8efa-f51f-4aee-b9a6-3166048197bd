<template>
  <Page title="Input Proposal Kebencanaan" :sidebar="true">
    <template v-slot:toolbar>
      <v-icon @click="doPrint++" v-tooltip="'Download Excel'"
        >mdi-microsoft-excel</v-icon
      >
    </template>
    <Sidebar v-model:value="area" :tabs="[2, 1]" :filter="{ HasPB: true }" />
    <div style="padding: 10px; width: 100%" v-show="area.Kelurahan">
      <DesaGlance
        :dbparams="area"
        :jmlUsulan="jmlUsulan"
        v-model:isApproved="isApproved"
        @clickTambahBaru="ClickTambahBaru"
      />
      <div
        style="padding: 0 5px 7px 5px; display: flex; width: 100%"
        v-if="this.datagrid.length"
      >
        <v-spacer />
        <v-btn
          small
          color="primary"
          @click="OpenProposalDesa"
          style="margin-right: 5px"
          :disabled="disabledDesa"
        >
          <v-icon left>mdi-file-document-multiple</v-icon>
          PROPOSAL DESA
        </v-btn>
        <!-- <v-btn size="small" color="warning" @click="OpenRAB">
          <v-icon left>mdi-shape-plus</v-icon>
          RAB
        </v-btn> -->
      </div>
      <Grid
        v-model:datagrid="datagrid"
        :dbref="dbref"
        :dbparams="area"
        :disabled="true"
        height="calc(100vh - 230px)"
        :doRebind="doRebind"
        :doPrint="doPrint"
        :columns="[
          {
            name: '',
            value: 'IsChecked',
            class: 'plain center',
          },
          {
            name: 'NIK',
            value: 'NIK',
            class: 'plain',
            filter: {
              type: 'search',
            },
          },
          {
            name: '',
            value: 'VerStatsID',
            class: 'plain center',
          },
          {
            name: 'Nama',
            value: 'KRT_Nama',
            width: '200px',
            class: 'fix-width',
            filter: {
              type: 'search',
              value: 'Nama',
            },
          },
          {
            name: 'Alamat',
            value: 'Alamat',
            width: '250px',
            class: 'fix-width',
            filter: {
              type: 'search',
            },
          },
          {
            name: 'Skor',
            value: 'ScoreTag',
          },
          {
            name: 'DT',
            value: 'NamaData',
          },
          {
            name: '',
            value: 'Dokumen',
            class: area.tabId == 2 ? '' : 'hide',
          },
          {
            name: '',
            value: 'HasMessage',
            class: 'plain center',
          },
        ]"
      >
        <template #row-IsChecked="{ row }">
          <Checkbox
            v-model:value="row.CheckedValue"
            checkedIcon="check_box"
            disabledIcon="mdi-lock"
            :disabled="Boolean(row.IsLocked)"
            @onClick="SubmitProposal(row.NIK, ...arguments)"
          />
        </template>
        <template #row-NIK="{ row }">
          <v-btn variant="text" size="small" color="primary" @click="OpenDetail(row.NoRef)">
            {{ row.NIK || '&lt; kosong &gt;' }}
          </v-btn>
        </template>
        <template #row-VerStatsID="{ row }">
          <v-icon
            v-if="row.VerStatsID >= 6"
            color="primary"
            v-tooltip="'Sudah Terverifikasi'"
          >
            mdi-account-check
          </v-icon>
          <v-icon v-if="row.VerStatsID < 6" v-tooltip="'Belum Terverifikasi'">
            mdi-account-question-outline
          </v-icon>
        </template>
        <template #row-Dokumen="{ row }">
          <v-icon
            v-if="row.IsComplete"
            color="success"
            v-tooltip="'Proposal Sudah Lengkap'"
            @click="OpenProposal(row.NIK)"
          >
            mdi-file-check
          </v-icon>
          <v-icon
            v-if="!row.IsComplete"
            @click="OpenProposal(row.NIK)"
            v-tooltip="'Proposal Belum Lengkap'"
          >
            mdi-file-alert-outline
          </v-icon>
          <v-icon
            style="margin-left: 8px"
            color="success"
            v-tooltip="'Disetujui Kabupaten'"
            v-if="row.KabKotaApproval"
          >
            mdi-shield-check
          </v-icon>
          <v-icon
            v-if="!row.KabKotaApproval && row.CheckedValue"
            style="margin-left: 8px"
            v-tooltip="'Belum Disetujui Kabupaten'"
          >
            mdi-shield-outline
          </v-icon>
        </template>
        <template v-slot:footer="{ columns }">
          <td :colspan="columns.length" v-if="area.ProposalID > 7">
            <div style="text-align: center; padding: 12px">
              Akan masuk halaman usulan sesuai urutan prioritas
            </div>
          </td>
        </template>
        <template #row-HasMessage="{ row }">
          <v-icon
            :color="row.HasMessage == 2 ? 'green' : 'silver'"
            v-tooltip="
              row.HasMessage == 2 ? 'Ada pesan baru' : 'tidak ada pesan baru'
            "
            @click="OpenMessages(row.NoRef)"
          >
            {{
              row.HasMessage ? 'mdi-message-text' : 'mdi-message-minus-outline'
            }}
          </v-icon>
        </template>
      </Grid>
      <ValidasiDetail
        v-model:show="showDetailModal"
        :noRef="selectedRef"
        :area="area"
        @save="doRebind++"
      />
      <ProposalDetail
        v-model:show="showProposalModal"
        :nik="selectedNIK"
        @save="doRebind++"
      />
      <ProposalDesa
        v-model:show="showProposalDesa"
        :kodeDagri="area.KodeDagri"
        :tahun="area.Tahun"
      />
      <RAB
        v-model:show="showRAB"
        :kodeDagri="area.KodeDagri"
        :tahun="area.Tahun"
      />
      <Messages
        :tahun="area.Tahun"
        :noRef="selectedRef"
        v-model:show="showMessages"
      />
    </div>
  </Page>
</template>
<script>
import Sidebar from './SideBar.vue'
import DesaGlance from '../DesaGlance.vue'
import ValidasiDetail from '../ValidasiData/ValidasiDetail.vue'
import ProposalDetail from './ProposalDetail.vue'
import ProposalDesa from './ProposalDesa.vue'
import Messages from '../ReviewUsulan/Messages.vue'
import RAB from './RAB.vue'

export default {
  components: {
    Sidebar,
    DesaGlance,
    ValidasiDetail,
    ProposalDetail,
    ProposalDesa,
    Messages,
    RAB,
  },
  data: () => ({
    priorcolors: ['#333', '#e53935', '#F4511E', '#43A047', '#1E88E5'],
    datagrid: [],
    area: {},
    showDetailModal: false,
    showProposalModal: false,
    showProposalDesa: false,
    showMessages: false,
    showRAB: false,
    selectedRef: null,
    doPrint: 0,
    doRebind: 0,
    selectedNIK: null,
    isApproved: null,
  }),
  computed: {
    dbref() {
      return 'BCN.ProposalDet'
    },
    jmlUsulan() {
      return this.datagrid.filter((d) => {
        return d.CheckedValue
      }).length
    },
    disabledDesa() {
      if (!this.datagrid.length) return true
      if (!this.datagrid[0].CheckedValue) return true
      return (
        this.datagrid.filter((d) => {
          return d.CheckedValue && !d.KabKotaApproval
        }).length > 0
      )
    },
  },
  watch: {
    showDetailModal(val) {
      if (!val) this.selectedRef = null
    },
    isApproved(val) {
      this.area = { ...this.area, isApproved: val }
    },
    '$route.query'() {
      this.area = this.$route.query
    },
  },
  mounted() {
    if (Object.keys(this.$route.query).length) {
      this.area = this.$route.query
    } else if (window.sessionStorage.getItem('side-area')) {
      setTimeout(() => {
        let areaVal = JSON.parse(window.sessionStorage.getItem('side-area'))
        this.$emit('update:value', areaVal)
      }, 500)
    }
  },
  methods: {
    ClickTambahBaru() {
      this.showDetailModal = true
    },
    OpenDetail(noRef) {
      this.selectedRef = noRef
      this.showDetailModal = true
    },
    OpenProposal(nik) {
      this.selectedNIK = nik
      this.showProposalModal = true
    },
    OpenMessages(noRef) {
      this.selectedRef = noRef
      this.showMessages = true
    },
    OpenRAB() {
      this.showRAB = true
    },
    OpenProposalDesa() {
      this.showProposalDesa = true
    },
    async SubmitProposal(nik, checked, callback) {
      var ret = await this.$api.call('BCN.SavProposalDetByNIK', {
        ProposalID: this.area.ProposalID,
        NIK: nik,
        IsAdd: checked,
        Sumber: this.area.tabId,
      })
      if (!ret.success) {
        callback(false)
      } else {
        if (this.datagrid.length == 1) this.doRebind++
      }
    },
  },
}
</script>
<style lang="scss">
.page-input-usulan {
  .ui-checkbox {
    .--box.checked {
      color: #1976d2;
    }
  }
}
</style>
