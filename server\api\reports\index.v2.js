var express = require("express");
const path = require("path");
const fs = require("fs");
const carbone = require("carbone");
var db = require("../../common/db");
var moment = require("moment");
var router = express.Router();
var Excel = require("exceljs");
const shell = require("shelljs");
moment.locale("id");

// CARBONE specific setup
carbone.addFormatters({
  // this formatter can be used in a template with {d.myBoolean:yesOrNo()}
  format: function(val, fmt) {
    if (val instanceof Date) {
      return moment(val).format(fmt.replace(/_/g, " "));
    } else if (!isNaN(val)) {
      return parseFloat(val)
        .toFixed(2)
        .replace(/\d(?=(\d{3})+\.)/g, "$&.")
        .replace(/\.00$/, " ");
    } else {
      return val;
    }
  },
  boolstr: function(val, fmt) {
    var noyes = fmt.split("|");
    if (val === undefined || val === null || val === "") return "";
    else if (val === 0 || val === false) return noyes[0];
    else return noyes[1];
  }
});

const report = {
  colswidth: {
    No: 6,
    NIK: 17,
    Nama: 24,
    KRT_Nama: 24,
    Alamat: 50,
    Kabupaten: 15,
    Kecamatan: 15,
    Kelurahan: 15,
    Desa: 15
  },
  async Generic(req, res, type) {
    try {
      res.header(
        "Cache-Control",
        "private, no-cache, no-store, must-revalidate"
      );
      res.header("Expires", "-1");
      res.header("Pragma", "no-cache");
      let param = { ...req.query, ...req.body };

      let rpt_name = param.rptname ? param.rptname : "Report";
      let dd = moment().format("mmss");
      let filename = rpt_name.replace(/[^a-z0-9]/gi, "_") + dd;
      const options = {
        filename: `tmp/${filename}.xlsx`,
        useStyles: true,
        useSharedStrings: true
      };
      let workbook = new Excel.stream.xlsx.WorkbookWriter(options);

      var sets = 1,
        i = 0;
      if (req.query.sets) sets = req.query.sets;

      do {
        let postfix = i > 0 ? "_s" + (i + 1) : "";
        let data = await db.exec(param.sp + postfix, param);

        report.GenericRun(data, workbook, {
          name: rpt_name,
          sheet: i,
          headers: param.headers,
          groupsheet: param.groupsheet
        });

        i++;
      } while (i < sets);

      await workbook.commit();
      if (type == "pdf" && this.GeneratePDF(`tmp/${filename}.xlsx`)) {
        res.send({
          success: true,
          data: `/get/${filename}.pdf`,
          message: "Report Generated",
          type: "url"
        });
      } else {
        res.send({
          success: true,
          data: `/get/${filename}.xlsx`,
          message: "Report Generated",
          type: "url"
        });
      }
    } catch (err) {
      res.status(400).send({
        error: err.message
      });
    }
  },
  GeneratePDF(path) {
    console.info(`Converting to PDF: ${path}..`);
    var out = shell.exec(
      `/opt/libreoffice7.0/program/soffice --convert-to pdf "/app/${path}" --outdir /app/tmp`,
      {
        silent: true
      }
    );
    if (out.code != 0) {
      console.error(`ERR: ${out.stderr}`);
      return false;
    } else return true;
  },
  GenerateHeader(sheet, keys) {
    let s_header = {
      border: {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" }
      },
      font: { size: 10, bold: true },
      alignment: { vertical: "middle", horizontal: "center" }
    };
    // Generating Dynamic Headers
    for (i = 0; i < keys.length; i++) {
      let colspan = 1;
      let j = i;
      let c1 = "",
        c2 = "";
      do {
        if (j + 1 >= keys.length) break;
        c1 = keys[j].split("_", 2);
        c2 = keys[j + 1].split("_", 2);
        if (c1[0] == c2[0]) colspan++;
        j++;
      } while (c1[0] == c2[0]);

      if (colspan == 1) {
        sheet.mergeCells(1, i + 1, 2, i + 1);
        sheet.getCell(1, i + 1).value = keys[i].replace("_", " ");
        sheet.getColumn(i + 1).width =
          this.colswidth[keys[i]] || Math.ceil(keys[i].length * 1.5);
        sheet.getCell(1, i + 1).border = s_header.border;
        sheet.getCell(2, i + 1).alignment = s_header.alignment;
      } else {
        // // array_push(hcol, ['value'=>c1[0], 'colspan'=>colspan]);
        sheet.mergeCells(1, i + 1, 1, i + colspan);
        sheet.getCell(1, i + 1).value = c1[0].replace("_", " ");
        sheet.getCell(1, i + 1).border = s_header.border;
        sheet.getCell(1, i + 1).alignment = s_header.alignment;
        let len = colspan + i;
        while (i < len) {
          let c2 = keys[i].split("_", 2);
          sheet.getCell(2, i + 1).value = c2[1].replace("_", " ");
          sheet.getCell(2, i + 1).border = s_header.border;
          sheet.getCell(2, i + 1).alignment = s_header.alignment;

          i++;
        }
        i--;
      }
    }
    sheet.getRow(2).font = s_header.font;
    sheet.getRow(1).font = s_header.font;
  },
  GenericRun(res, workbook, opts) {
    let s_header = {
      border: {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" }
      },
      font: { size: 10, bold: true },
      alignment: { vertical: "middle", horizontal: "center" }
    };
    try {
      // Common columns width
      if (opts.groupsheet) {
        let group_col = opts.groupsheet;
        let group_val = "";

        let hcol = [];
        let col_w = [];

        let i = 0;
        let sheet = null;
        res.forEach(row => {
          if (group_val != row[group_col]) {
            group_val = row[group_col];
            sheet = workbook.addWorksheet(group_val);
            this.GenerateHeader(sheet, Object.keys(row));
          }

          let rcol_arr = [];
          for (let k in row) {
            rcol_arr.push(row[k]);
          }
          sheet.addRow(rcol_arr); // body
        });
      } else {
        if (res.length > 0) {
          let hcol = [];
          let hcol2 = [];
          let col_w = [];
          let i = 1;
          keys = Object.keys(res[0]);
          let sheet = workbook.addWorksheet("Report");

          sheet.pageSetup.fitToPage = true;
          sheet.pageSetup.fitToWidth = 1;
          sheet.pageSetup.fitToHeight = 0;
          sheet.pageSetup.paperSize = 9;
          sheet.pageSetup.orientation = "landscape";
          sheet.pageSetup.margins = {
            left: 0.25,
            right: 0.25,
            top: 0.5,
            bottom: 0.5,
            header: 0.3,
            footer: 0.3
          };

          if (opts.headers) {
            sheet.addRow(
              opts.headers.map((h, idx) => {
                sheet.getColumn(idx + 1).width = this.colswidth[h.value]
                  ? this.colswidth[h.value]
                  : String(res[0][h.value]).length * 1.3 < 6
                  ? 6
                  : String(res[0][h.value]).length * 1.3;
                return h.name;
              })
            );
            sheet.getRow(1).font = s_header.font;
            sheet.getRow(1).commit();

            res.forEach(row => {
              let rcol_arr = [];
              for (let i in opts.headers) {
                rcol_arr.push(row[opts.headers[i].value]);
              }
              sheet.addRow(rcol_arr).commit(); // body
            });
          } else {
            this.GenerateHeader(sheet, keys);

            res.forEach(row => {
              let rcol_arr = [];
              for (let key in row) {
                // if(is_numeric(row[key]) && strlen(row[key]) < 15)
                //     array_push($rcol_arr, ['value'=>floatval($value), 'style'=>['NumFormat'=>1]]);
                // else
                rcol_arr.push(row[key]);
              }
              sheet.addRow(rcol_arr).commit(); // body
            });
          }
          sheet.commit();
        }
      }
    } catch (ex) {
      console.error(ex);
    }
  }
};

router.post("/GetParamsVue", async function(req, res) {
  var dbp = await db.pool.query(
    `SELECT * FROM information_schema.PARAMETERS
    WHERE SPECIFIC_SCHEMA = '${db.dbname}'
    AND SPECIFIC_NAME = '${req.body.sp}' AND PARAMETER_MODE = 'IN'`
  );

  let sb = [];
  for (idx in dbp) {
    var param_name = dbp[idx].PARAMETER_NAME; //.substr(1);
    if (param_name.substr(-3) !== "Ref") {
      var o = {};
      o["id"] = param_name;
      if (param_name.substr(-2) == "ID")
        //sb += '"Name":"' + param_name.substr(8, param_name.length - 10) + '",';
        o["text"] = param_name.substr(8, param_name.length - 10);
      // sb += '"Name":"' + param_name.substr(1) + '",';
      else o["text"] = param_name.substr(1);
      if (param_name.substr(-2) === "ID") {
        // sb +=
        //   '"url":"api/call/' +
        //   param_name.substr(1, param_name.length - 3) +
        //   '",';
        o["dbref"] = param_name.substr(1, param_name.length - 3);
        //db += '"Type":"Select",';
        o["type"] = "Select";
      } else if (
        dbp[idx].DATA_TYPE == "datetime" ||
        dbp[idx].DATA_TYPE == "varchar"
      ) {
        // db += '"Type":"Input",';
        o["type"] = "Input";
      } else if (
        dbp[idx].DATA_TYPE == "datetime" ||
        dbp[idx].DATA_TYPE == "date"
      ) {
        // db += '"Type":"Date",';
        o["type"] = "Date";
      } else if (
        dbp[idx].DATA_TYPE == "boolean" ||
        dbp[idx].DATA_TYPE == "tinyint"
      ) {
        // db += '"Type":"Checkbox",';
        o["type"] = "Checkbox";
      } else {
        db += '"Type":"' + dbp[idx].DATA_TYPE + '"';
      }
      // db += "},";
      sb.push(o);
    }
  }
  // sb += "{";
  // sb += '"Id":"btnReport",';
  // sb += '"Name":"",';
  // sb += '"Type":"VBtn"';
  // sb += "}";
  // sb += "]";

  res.send({
    success: true,
    data: sb,
    message: "",
    type: "array"
  });
});

router.post("/GetParams", async function(req, res) {
  var dbp = await db.pool.query(
    `SELECT * FROM information_schema.PARAMETERS
    WHERE SPECIFIC_SCHEMA = '${db.dbname}'
    AND SPECIFIC_NAME = '${req.query.sp}' AND PARAMETER_MODE = 'IN'`
  );

  let sb = "[";
  for (idx in dbp) {
    var param_name = dbp[idx].PARAMETER_NAME; //.substr(1);
    if (param_name.substr(-3) !== "Ref") {
      sb += "{";
      sb += '"Id":"' + param_name + '",';
      if (param_name.substr(-2) == "ID")
        sb += '"Name":"' + param_name.substr(8, param_name.length - 10) + '",';
      else sb += '"Name":"' + param_name.substr(1) + '",';
      if (param_name.substr(-2) === "ID") {
        sb +=
          '"url":"api/call/' +
          param_name.substr(1, param_name.length - 3) +
          '",';
        db += '"Type":"select",';
      } else if (
        dbp[idx].DATA_TYPE == "datetime" ||
        dbp[idx].DATA_TYPE == "varchar"
      ) {
        db += '"Type":"text",';
      } else if (
        dbp[idx].DATA_TYPE == "datetime" ||
        dbp[idx].DATA_TYPE == "date"
      ) {
        db += '"Type":"date",';
      } else if (
        dbp[idx].DATA_TYPE == "boolean" ||
        dbp[idx].DATA_TYPE == "tinyint"
      ) {
        db += '"Type":"checkbox",';
      } else {
        db += '"Type":"' + dbp[idx].DATA_TYPE + '"';
      }
      db += "},";
    }
  }
  sb += "{";
  sb += '"Id":"btnReport",';
  sb += '"Name":"",';
  sb += '"Type":"button"';
  sb += "}";
  sb += "]";

  res.send({
    Success: true,
    Data: sb,
    Message: "",
    Type: "array"
  });
});

router.post("/Generic/:type", async function(req, res) {
  // let rid = req.query.rid.split("/");
  await report.Generic(req, res, req.params.type);
});

router.get("/get/:file", async function(req, res) {
  res.sendFile(path.resolve("tmp/" + req.params.file));
});

router.get("/get/templates/:file", async function(req, res) {
  res.sendFile(path.resolve("tmp/templates/" + req.params.file));
});

router.post("/template/:file", async function(req, res) {
  let data = await db.exec("PRM_RptKuisoner", req.body);
  // console.log(data);
  carbone.render("tmp/templates/" + req.params.file, data, function(
    err,
    result
  ) {
    if (err) {
      return console.error(err);
    }
    // write the result
    fs.writeFileSync("tmp/" + req.params.file, result);
    res.send({
      success: true,
      data: `/get/${req.params.file}`,
      message: "Report Generated",
      type: "url"
    });
  });
});

router.post("/generate/:type", async function(req, res) {
  try {
    // if (req.body.headers) {
    //   let data = req.body.data;
    //   if (req.body.sp) {
    //     console.log("Loading data..");
    //     data = await db.exec(req.body.sp, req.body);
    //     // console.log("Mapping data..");
    //     // data = data.map(d => {
    //     //   let r = [];
    //     //   for (let i = 0; i < req.body.cols.length; i++) {
    //     //     r.push(d[req.body.cols[i]]);
    //     //   }
    //     //   return r;
    //     // });
    //   }

    //   console.log("Generating tables..");
    //   let dd = moment().format("mmss");
    //   const options = {
    //     filename: `tmp/table${dd}.xlsx`,
    //     useStyles: true,
    //     useSharedStrings: true
    //   };
    //   let workbook = new Excel.stream.xlsx.WorkbookWriter(options);
    //   // let workbook = new Excel.Workbook();
    //   let ws = workbook.addWorksheet(req.body.name || "Report");

    //   ws.pageSetup.fitToPage = true;
    //   ws.pageSetup.fitToWidth = 1;
    //   ws.pageSetup.fitToHeight = 0;
    //   ws.pageSetup.paperSize = 9;
    //   ws.pageSetup.orientation = "landscape";
    //   ws.pageSetup.margins = {
    //     left: 0.25,
    //     right: 0.25,
    //     top: 0.5,
    //     bottom: 0.5,
    //     header: 0.3,
    //     footer: 0.3
    //   };

    //   ws.addTable({
    //     name: req.body.name || "Report",
    //     ref: "A1",
    //     headerRow: true,
    //     totalsRow: true,
    //     style: {
    //       theme: "TableStyleLight8",
    //       showRowStripes: true
    //     },
    //     columns: req.body.headers
    //     // rows: data
    //   });
    //   //workbook.xlsx.writeFile(`tmp/tables${dd}.xlsx`).then(() => {
    //   await workbook.commit();
    //   if (
    //     req.params.type == "pdf" &&
    //     report.GeneratePDF(`tmp/tables${dd}.xlsx`)
    //   ) {
    //     res.send({
    //       success: true,
    //       data: `/get/tables${dd}.pdf`,
    //       message: "Report Generated",
    //       type: "url"
    //     });
    //   } else {
    //     res.send({
    //       success: true,
    //       data: `/get/tables${dd}.xlsx`,
    //       message: "Report Generated",
    //       type: "url"
    //     });
    //   }
    //   // });
    // } else
    if (req.query.sp || req.body.sp) {
      await report.Generic(req, res, req.params.type);
    } else {
      res.send({
        success: true,
        data: null,
        message: "Cant Generate Report",
        type: "error"
      });
    }
  } catch (ex) {
    res.send({
      success: false,
      data: ex.message,
      message: "Error while generating report",
      type: "error"
    });
  }
});

// router.get("/permohonan/:id", uji.rptPermohonan);
// router.get("/permohonan-paid/:id", uji.rptPermohonanPaid);
// router.get("/permohonanonline/:id", uji.rptPermohonanOnline);
// router.get("/spu/:id", uji.rptPerintahUji);

module.exports = router;
