<template>
  <div class="sidebar-validasi">
    <div style="padding: 10px; display: flex">
      <XSelect
        v-show="!searchMode"
        dbref="PRM.SelPBDTCity"
        :dbparams="{ nocache: true }"
        v-model:value="kabupaten"
        :valueAsObject="true"
      />
      <XInput
        type="text"
        v-show="searchMode"
        v-model:value="keyword"
        placeholder="Cari .."
      />
      <v-icon
        style="height: 27px; margin-left: 10px"
        v-show="!searchMode"
        @click="searchMode = !searchMode"
        >mdi-magnify</v-icon
      >
      <v-icon
        style="height: 27px; margin-left: 10px"
        v-show="searchMode"
        @click="searchMode = !searchMode"
        >mdi-close</v-icon
      >
    </div>
    <div>
      <List
        dbref="BCN.SelValidasiArea"
        :dbparams="{
          Kabupaten: kabupaten.txt,
        }"
        :filters="{
          keyword: keyword,
          filter: filterArea,
        }"
        height="calc(100vh - 165px)"
        @itemClick="AreaClicked"
      >
        <template #default="{ row }">
          <div :class="`ordr-${row.Ordr}`">
            <div>{{ row.AreaName }}</div>
            <div class="status">
              <div
                class="badge s-jml"
                v-tooltip="
                  `PB: ${row.SkorPB} / Validasi: ${row.SudahValidasi} / Total: ${row.Total}`
                "
              >
                {{ row.SkorPB }} / {{ row.SudahValidasi }} / {{ row.Total }}
              </div>
            </div>
          </div>
        </template>
      </List>
    </div>
  </div>
</template>
<script>
import { mapActions } from 'vuex'
export default {
  data: () => ({
    kabupaten: { val: null, txt: null },
    selectedArea: {},
    searchMode: false,
    keyword: '',
  }),
  props: {
    value: Object,
  },
  methods: {
    ...mapActions(['setPageFocused']),
    AreaClicked(item) {
      if (!item || !item.AreaID) return
      document.getElementsByClassName('page')[0].scroll({
        left: 340,
        behavior: 'smooth',
      })
      this.setPageFocused(true)
      this.$emit('update:value', {
        Kabupaten: this.kabupaten.txt,
        Kecamatan: item.Kecamatan,
        Kelurahan: item.AreaName,
        KelurahanID: item.AreaID,
      })
    },
    filterArea(item) {
      return item.AreaName.match(new RegExp(this.keyword, 'i'))
    },
  },
}
</script>
<style lang="scss">
.sidebar-validasi {
  background: white;
  .ui-list {
    .--item {
      border-bottom: 1px solid #ddd;
      padding: 5px 8px;
      font-size: 12px;
      .ordr-1 {
        font-weight: bold;
        .status {
          display: none;
        }
      }
      .ordr-2 {
        padding-left: 10px;
        display: flex;

        .status {
          justify-content: flex-end;
          display: flex;
          flex: 1;

          .badge {
            padding-right: 9px;
            padding-left: 9px;
            border-radius: 9px;
          }

          .s-jml {
            background-color: #f3f3f3;
          }
        }
      }
    }
  }
}
</style>
