<template>
  <Modal title="Ganti Penerima" v-model:show="x_show" @onSubmit="Submit">
    <Search
      label="Calon Pengganti"
      dbref="PRM.SelPBDT"
      :dbparams="dbparams"
      v-model:value="niknew"
      valueKey="NIK"
      textKey="KRT_Nama"
    />
  </Modal>
</template>
<script>
export default {
  data: () => ({
    x_show: false,
    niknew: null,
  }),
  props: {
    nik: [String, Number],
    show: Boolean,
    dbparams: Object,
  },
  watch: {
    show(val) {
      this.x_show = val
    },
    x_show() {
      this.$emit('update:show', this.x_show)
    },
  },
  methods: {
    async Submit() {
      await this.$api.call('BLG.SavGantiPenerima', {
        NIKPREV: this.nik,
        NIKNOW: this.niknew,
      })
      this.$emit('success')
      this.$emit('update:show', false)
    },
  },
}
</script>
<style lang="scss"></style>
