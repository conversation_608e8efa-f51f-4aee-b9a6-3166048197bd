<template>
  <v-container style="max-width: 100vw; padding: 0">
    <div class="login-box">
      <div
        style="
          height: 100px;
          background: url('/imgs/appex/banner.png');
          background-size: cover;
          background-position: center;
        "
      ></div>
      <div style="padding: 30px">
        <XInput
          v-model:value="forms.username"
          label="Username"
          class="inline"
          left-icon="mdi-face-man"
          width="100%"
        />
        <XInput
          v-model:value="forms.password"
          label="Password"
          type="password"
          left-icon="mdi-lock"
          width="100%"
        />
        <br />
        <v-btn color="primary" style="width: 210px" @click="submit">
          Login
        </v-btn>
      </div>
      <v-btn
        v-show="!showNews"
        v-if="false"
        text
        small
        color="primary"
        style="margin-top: -20px"
        @click="showNews = true"
      >
        <v-icon left>mdi-information-outline</v-icon> Apa yang baru?
      </v-btn>
    </div>

    <div
      style="
        height: 80px;
        background: rgba(255, 255, 255, 0.5);
        display: flex;
        justify-content: center;
        position: absolute;
        width: 100vw;
        bottom: 0;
      "
    >
      <div
        style="
          display: flex;
          align-self: center;
          text-align: center;
          width: 50vw;
          min-width: 350px;
        "
      >
        <a
          href="http://datartlh.perumahan.pu.go.id/"
          target="_blank"
          style="flex: 1"
        >
          <img src="/imgs/ertlh.png" height="30px" />
        </a>

        <a
          href="https://caribdt.dinsos.jatengprov.go.id/public/dashboard"
          target="_blank"
          style="flex: 1"
        >
          <img
            src="https://sidesa.jatengprov.go.id/img/onscreen/home/<USER>"
            height="30px"
          />
        </a>

        <a
          href="https://caribdt.dinsos.jatengprov.go.id/public/dashboard"
          target="_blank"
          style="flex: 1"
        >
          <img src="/imgs/caribdt-min.png" height="30px" />
        </a>
      </div>
    </div>
    <NewsUpdate :show="showNews" />
  </v-container>
</template>

<script>
import NewsUpdate from './NewsUpdate.vue'
import { mapActions } from 'vuex'
export default {
  components: {
    NewsUpdate,
  },
  data: () => ({
    forms: {
      username: '',
      password: '',
    },
    showNews: false,
  }),
  mounted() {
    localStorage.clear()
    sessionStorage.clear()
    this.setMenu(null)
  },
  methods: {
    ...mapActions(['setMenu', 'setUser', 'setIgahpUser']),
    getListMenu(menu) {
      let mlist = {}
      menu.forEach((m) => {
        if (m.child && m.child.length) {
          if (m.MenuUrl) {
            mlist[m.MenuUrl] = this.getListMenu(m.child)
          } else mlist = Object.assign(mlist, this.getListMenu(m.child))
        } else if (m.MenuUrl) mlist[m.MenuUrl] = m.RWX
      })
      return mlist
    },
    async loginIGAHP() {
      let d = await this.$api.post('/api/igahp/login', this.forms)
      if (d.success) {
        this.setIgahpUser({
          username: this.forms.username,
          accessToken: d.data.accessToken,
        })
        // sessionStorage.setItem(
        //   'igahpUser',
        //   JSON.stringify({
        //     username: this.forms.username,
        //     accessToken: d.data.accessToken,
        //   })
        // )
      }
      return d
    },
    async submit() {
      let ret = null
      if (this.forms.username.match(/@[a-z0-9.]+$/)) {
        ret = await this.loginIGAHP()
        console.log(ret)
        if (ret.success) {
          ret = await this.$api.login(this.forms)
        } else {
          this.$api.notify('Login Gagal', 'error')
          return
        }
      } else {
        ret = await this.$api.login(this.forms)
      }
      if (ret && ret.length) {
        this.setUser(ret[0])
        let menu = await this.$api.call('Arch_SelMenu')
        if (menu) {
          this.setMenu(menu)
          localStorage.setItem(
            'menu-access',
            JSON.stringify(this.getListMenu(menu.data))
          )
          if (ret[0].HomeUrl) this.$router.push({ path: ret[0].HomeUrl })
          else this.$router.push({ name: 'Home' })
        }
      }
    },
  },
}
</script>
<style lang="scss">
// .theme--light.v-application {
//   background: #f3f3f3;
// }
.login-box {
  width: 300px;
  background: white;
  box-sizing: content-box;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  margin: auto;
  margin-top: 10%;
  text-align: center;
  overflow: hidden;
}
</style>
