const fs = require('fs')

class HtmlGenerator {
  constructor (opts) {
    this.worksheets = {}
    this.names = []
    this.filename = opts.filename

    fs.writeFileSync(
      this.filename,
      `<link rel="preconnect" href="https://fonts.gstatic.com">
       <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600&display=swap" rel="stylesheet">
       <style>
          table {
            font-family: 'Montserrat', 
            sans-serif; font-size:12px;
            background: white;
            box-shadow: 0 0 5px rgb(0 0 0 / 30%);
            display: none;
            border-collapse: collapse;
          } 
          td {
            padding:4px;
            border: 1px solid #ddd;
          } 
          td:first-child {
            border-left: 1px solid black;
          }
          td:last-child {
            border-right: 1px solid black;
          }
          tr:last-child td {
            border-bottom: 1px solid black;
          }
          .sheetsTab {
            position: fixed;
            bottom: 0;
            display: flex;
            box-shadow: 0 0 5px rgb(0 0 0 / 30%);
            font-family: 'Montserrat', sans-serif;
            font-size: 14px;
          }
          .sheet {
            padding: 8px 10px;
            background: #f3f3f3;
            cursor: pointer;
          }
          .sheet.active {
            font-weight:bold;
            background:white;
          }
          body {
            padding: 0 20px;
            background: #f3f3f3;
          }
       </style>
       <script>
          function activateTab(sender, name) {
            let tables = document.getElementsByTagName('TABLE');
            for(let t of tables) {
              t.style.display = 'none';
            }
            document.getElementById(name).style.display = 'table';

            let sheets = document.getElementsByClassName('sheet');
            for(let s of sheets) {
              s.style.background = '#f3f3f3';
            }
            sender.style.background = 'white';
          }
       </script>
       `
    )
  }

  addWorksheet (sheetName) {
    const sheet = new HtmlWorksheet({
      name: sheetName,
      commiter: this.sheetCommit.bind(this)
    })
    this.worksheets[sheetName] = sheet
    this.names.push(sheetName)
    return sheet
  }

  sheetCommit (sheetName, rendered) {
    // const sheetTab = `<div class="sheet">${sheetName}</div>`
    // console.log('sheet commiter: ', rendered)
    delete this.worksheets[sheetName]
    if (fs.existsSync(this.filename)) fs.appendFileSync(this.filename, rendered)
    else fs.writeFileSync(this.filename, rendered)
  }

  commit () {
    // console.log(`workbook commit`, this.worksheets)
    for (const k in this.worksheets) {
      this.worksheets[k].commit()
    }
    let rendered = '<script>document.getElementsByTagName(\'TABLE\')[0].style.display = \'table\';</script>'
    if (this.names.length > 1) {
      rendered += '<div class="sheetsTab">'
      for (const name of this.names) {
        rendered += `<div class="sheet" onclick="activateTab(this, '${name}')">${name}</div>`
      }
      rendered += '</div>'
    }
    fs.appendFileSync(this.filename, rendered)
  }
}

class HtmlWorksheet {
  constructor (opts) {
    this.name = opts.name
    this.pageSetup = {}
    this.rows = {}
    this.renderedRow = []
    this.columns = []
    this.commiter = opts.commiter
    this.lastRowIdx = 0
    this.skippedCells = {}
  }

  addRow (cols) {
    const row = this.getRow(this.lastRowIdx + 2)
    if (cols) row.addColumns(cols)
    this.lastRowIdx++
    return row
  }

  getRow (idx) {
    idx = idx - 1
    if (!this.rows['row-' + idx]) {
      const row = new HtmlRow({
        rowIdx: idx,
        commiter: this.rowCommit.bind(this)
      })
      this.rows['row-' + idx] = row
    }
    return this.rows['row-' + idx]
  }

  getColumn (idx) {
    idx -= 1
    if (!this.columns[idx]) {
      this.columns[idx] = {}
    }
    return this.columns[idx]
  }

  getCell (rowIdx, colIdx) {
    rowIdx = rowIdx - 1
    if (this.lastRowIdx < rowIdx) this.lastRowIdx = rowIdx

    return this.getRow(rowIdx + 1).getColumn(colIdx)
  }

  mergeCells (startRow, startCol, endRow, endCol) {
    startRow = startRow - 1
    startCol = startCol - 1
    endRow = endRow - 1
    endCol = endCol - 1

    // console.log(
    //   'merge: ',
    //   'r',
    //   startRow,
    //   'c',
    //   startCol,
    //   ' rowspan:',
    //   endRow - startRow,
    //   ' colspan:',
    //   endCol - startCol
    // )
    for (let rx = startRow; rx <= endRow; rx++) {
      for (let cx = startCol; cx <= endCol; cx++) {
        if (cx !== startCol || rx !== startRow) {
          this.skippedCells[`${rx}:${cx}`] = true
          // console.log('isMerged: ', rx + 1, cx + 1)
          this.getRow(rx + 1).getColumn(cx + 1).isMerged = true
        }
      }
      this.getRow(rx + 1).skippedCells = this.skippedCells
    }

    this.getRow(startRow + 1).getColumn(startCol + 1).colspan =
      endCol - startCol + 1
    this.getRow(startRow + 1).getColumn(startCol + 1).rowspan =
      endRow - startRow + 1
  }

  rowCommit (idx, stringRender) {
    // console.log('row commit:', idx, stringRender)
    delete this.rows['row-' + idx]
    this.renderedRow[idx] = stringRender
  }

  commit () {
    for (const k in this.rows) {
      this.rows[k].commit()
    }
    let rendered = ''
    for (const r of this.renderedRow) {
      rendered += r
    }
    rendered = `<table id="${this.name}" style="border-collapse:collapse;">${rendered}</table>`
    this.commiter(this.name, rendered)
  }
}

class HtmlRow {
  constructor (opts) {
    this.rowIdx = opts.rowIdx
    this.commiter = opts.commiter
    this.columns = []
    this.skippedCells = {}
  }

  addColumns (cols) {
    // hack
    if (this.rowIdx === 1) return
    // console.log(this.rowIdx, cols)
    this.columns = []
    let idx = 0
    for (const c of cols) {
      // let opts = {}
      const col = this.getColumn(idx + 1)
      switch (typeof c) {
      case 'string':
        if (c.match(/^\d+%$/)) {
          col.alignment = {horizontal: 'right'}
        }
        col.value = c.replace(/\s/g, '&nbsp;')
        break
      case 'number':
        col.alignment = {horizontal: 'right'}
        col.value = c.toLocaleString()
        break
      default:
        break
      }
      idx++
    }
  }

  getColumn (idx) {
    idx = idx - 1
    if (!this.columns[idx]) {
      this.columns[idx] = new HtmlColumn({
        rowIdx: this.rowIdx,
        colIdx: idx
      })
    }
    return this.columns[idx]
  }

  commit () {
    if (this.rowIdx === 1000) {
      this.commiter(
        this.rowIdx,
        `<tr><td colspan="${this.columns.length}"> Download Excel for full list data. </td></tr>`
      )
      this.columns = []
      return
    }
    if (this.rowIdx > 1000) {
      this.columns = []
      return
    }
    let rendered = ''
    let cx = 0
    // console.log(this.rowIdx, this.skippedCells)
    for (const c of this.columns) {
      if (!this.skippedCells || !this.skippedCells[`${this.rowIdx}:${cx}`]) { rendered += c ? c.render() : '<td></td>' }
      cx++
    }

    let style = ''
    if (this.font) {
      if (this.font.size) style += `font-size:${this.font.size + 2}px;`
      if (this.font.bold) style += 'font-weight:bold;'
    }

    rendered = `<tr style="${style}">${rendered}</tr>`
    // console.log(`Row ${this.rowIdx}:`, rendered)
    this.commiter(this.rowIdx, rendered)
    this.columns = []
  }
}

class HtmlColumn {
  constructor (opts) {
    this._value = opts ? opts.value || '' : ''
    this.rowIdx = opts.rowIdx
    this.colIdx = opts.colIdx
    this.border = {
      //   top: { style: 'thin' },
      //   left: { style: 'thin' },
      //   bottom: { style: 'thin' },
      //   right: { style: 'thin' },
    }
    this.colspan = 0
    this.rowspan = 0
    this.isMerged = false

    this.borderStyle = {
      thin: '1px'
    }
    this.alignment = {vertical: 'middle', horizontal: 'left'}
  }

  set value (val) {
    // console.log(this.rowIdx, ':', this.colIdx, ' => ', val)
    this._value = val
  }

  get value () {
    return this._value
  }

  render () {
    // console.log(this.rowIdx, ':', this.colIdx, ' isMerged: ', this.isMerged)
    if (this.isMerged) return ''

    let styles = ''
    styles += `text-align:${this.alignment.horizontal};`

    if (this.border.top) {
      styles += `border-top:${
        this.borderStyle[this.border.top.style]
      } solid black;`
    }
    if (this.border.left) {
      styles += `border-left:${
        this.borderStyle[this.border.top.style]
      } solid black;`
    }
    if (this.border.right) {
      styles += `border-right:${
        this.borderStyle[this.border.top.style]
      } solid black;`
    }
    if (this.border.bottom) {
      styles += `border-bottom:${
        this.borderStyle[this.border.top.style]
      } solid black;`
    }

    let colspan = ''
    let rowspan = ''
    if (this.colspan > 1) colspan = `colspan="${this.colspan}"`
    if (this.rowspan > 1) rowspan = `rowspan="${this.rowspan}"`

    return `<td ${colspan} ${rowspan} style="${styles}">${this.value}</td>`
  }
}

module.exports = HtmlGenerator
