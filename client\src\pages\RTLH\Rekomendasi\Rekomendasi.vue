<template>
  <Page title="Rekomendasi" :sidebar="true">
    <template v-slot:toolbar>
      <v-icon @click="doPrint++" v-tooltip="'Download Excel'"
        >mdi-microsoft-excel</v-icon
      >
    </template>
    <Sidebar v-model:value="area" :rebind="rebindSidebar" :tabs="[2, 4]" />
    <div style="padding: 0 10px; width: 100%" v-show="area.Kelurahan">
      <DesaGlance
        :dbparams="area"
        :jmlUsulan="jmlUsulan"
        @clickTambahBaru="ClickTambahBaru"
      />
      <Grid
        v-model:datagrid="datagrid"
        :dbref="dbref"
        :dbparams="area"
        :filter="filterGrid"
        :disabled="true"
        :doPrint="doPrint"
        :doRebind="doRebind"
        height="calc(100vh - 243px)"
        :columns="[
          {
            name: '',
            value: 'IsChecked',
            class: 'plain center',
          },
          {
            name: 'RK',
            value: 'Tahapan',
            class: 'center',
          },
          {
            name: 'NIK',
            value: 'NIK',
          },
          {
            name: 'Nama',
            value: 'KRT_Nama',
            width: '220px',
            class: 'plain fix-width',
          },
          {
            name: 'Alamat',
            value: 'Alamat',
            width: '250px',
            class: 'fix-width',
          },
          {
            name: 'Skor',
            value: 'ScoreTag',
          },
          {
            name: 'Prior',
            value: 'Prioritas',
            class: 'center',
          },
          {
            name: 'DT',
            value: 'TipeData',
          },
          {
            name: '',
            value: 'Dokumen',
            class: 'plain center',
          },
          {
            name: '',
            value: 'Checklist',
            class: 'plain center',
          },
          {
            name: '',
            value: 'Approval',
            class: 'plain center',
          },
        ]"
      >
        <template #row-NIK="{ row }">
          <nik-block :nik="row.NIK" />
        </template>
        <template #row-IsChecked="{ row }">
          <Checkbox
            v-tooltip="row.IsApproved ? '' : 'Belum Disetujui'"
            :disabled="!row.IsApproved"
            v-model:value="row.IsSelected"
            checkedIcon="check_box"
            @click="SetSelected"
          />
        </template>
        <template #row-Tahapan="{ row }">
          <!-- <v-icon
            v-tooltip="row.Tahapan"
            v-if="row.Tahapan && row.Tahapan.replace('REKOM ', '') < 11"
            :class="'s-' + (row.Tahapan ? row.Tahapan.replace(/\s/, '') : '')"
          >
            mdi-numeric-{{
              row.Tahapan ? row.Tahapan.replace('REKOM ', '') : ''
            }}-box
          </v-icon>
          <div
            v-else-if="row.Tahapan"
            style="
              font-size: 10px;
              padding: 1px 3px;
              border-radius: 3px;
              border: 1px solid silver;
            "
          >
            {{ row.Tahapan.replace('REKOM ', '') }}
          </div> -->
          <rekom-block :rekom="row.Tahapan?.replace('REKOM ', '')" />
        </template>
        <template #row-KRT_Nama="{ row }">
          <div style="display: flex">
            <v-btn
              text
              small
              color="primary"
              @click="OpenDetail(row.NoRef)"
              style="width: 165px; justify-content: left"
            >
              {{ row.KRT_Nama }}
            </v-btn>
            <v-spacer />
            <v-icon
              v-if="IsRekom"
              color="red"
              v-tooltip="'Ganti Penerima'"
              @click="OpenGantiPenerima(row.NIK)"
              >mdi-account-switch-outline</v-icon
            >
          </div>
        </template>
        <template #row-Prioritas="{ row }">
          <v-icon
            :color="priorcolors[row.Prioritas]"
            v-tooltip="`Prioritas ${row.Prioritas}`"
          >
            mdi-numeric-{{ row.Prioritas }}-circle
          </v-icon>
        </template>
        <template #row-TipeData="{ row }">
          {{ parseInt((row.TipeData + '').substr(-2)) + 2000 }}
        </template>
        <template #row-Dokumen="{ row }">
          <v-icon
            v-tooltip="'Proposal'"
            v-if="row.IsComplete"
            color="success"
            @click="OpenProposal(row.NIK)"
            >mdi-file-check</v-icon
          >
          <v-icon v-if="!row.IsComplete" @click="OpenProposal(row.NIK)"
            >mdi-file-alert-outline</v-icon
          >
          <v-icon
            v-tooltip="'Berkas'"
            v-if="row.IsBerkasComplete"
            color="primary"
            @click="OpenBerkas(row.KodeDagri, row.Tahun, row.Tahapan, row.NIK)"
            >mdi-file-check</v-icon
          >
          <v-icon
            v-tooltip="'Berkas'"
            v-if="!row.IsBerkasComplete"
            @click="OpenBerkas(row.KodeDagri, row.Tahun, row.Tahapan, row.NIK)"
            color="warning"
            >mdi-file-alert-outline</v-icon
          >
        </template>
        <template #row-Checklist="{ row }">
          <v-icon
            v-tooltip="'Checklist'"
            v-if="false"
            @click="OpenChecklist(row.NIK)"
            style="margin-right: 5px"
          >
            mdi-clipboard-list
          </v-icon>
          <v-icon
            v-show="row.NoRekening"
            v-tooltip="row.NamaRekening + ' : ' + row.NoRekening"
          >
            mdi-bank
          </v-icon>
        </template>
        <template v-slot:footer="{ columns }">
          <tr v-if="area.ProposalID == currentYear - 2013">
            <td
              :colspan="columns.length"
              style="text-align: center; padding: 5px"
            >
              <div
                v-show="!buttonStatus.IsRekom && !buttonStatus.IsCancel"
                style="padding: 9px"
              >
                Pilih yang mau di-Rekom/dibatalkan Rekom nya
              </div>
              <v-btn
                color="primary"
                v-show="buttonStatus.IsRekom && !buttonStatus.IsCancel"
                @click="OpenChecklist(null)"
                style="margin: 0 8px"
              >
                CHECKLIST
              </v-btn>
              <v-btn
                color="success"
                v-show="buttonStatus.IsRekom && !buttonStatus.IsCancel"
                @click="OpenRekening(1)"
                style="margin: 0 8px"
              >
                REKOMENDASIKAN
              </v-btn>
              <v-btn
                v-show="!buttonStatus.IsRekom && buttonStatus.IsCancel"
                @click="Rekom(0)"
                style="margin: 0 8px"
              >
                BATALKAN REKOM
              </v-btn>
              <MenuButton
                :menu="rekomOpts"
                @item-click="GantiRekom($event)"
                v-show="!buttonStatus.IsRekom && buttonStatus.IsCancel"
              >
                <template #default="{ on }">
                  <v-btn
                    v-on="on"
                    style="margin: 0 8px"
                    v-show="
                      !buttonStatus.IsRekom &&
                      buttonStatus.IsCancel &&
                      tahapan != 'xxx'
                    "
                  >
                    GANTI REKOM
                  </v-btn>
                </template>
              </MenuButton>
              <v-btn
                v-show="!buttonStatus.IsRekom && buttonStatus.IsCancel"
                @click="showRekening = true"
                style="margin: 0 8px"
              >
                UBAH REKENING
              </v-btn>
            </td>
          </tr>
        </template>
      </Grid>
      <ValidasiDetail
        v-model:show="showDetailModal"
        :noRef="selectedRef"
        :area="area"
      />
      <ProposalDetail v-model:nik="selectedNIK" :show="showProposalModal" />
      <BerkasDetail v-model:param="berkasParam" :show="showBerkasModal" />
      <ChecklistRekom v-model:nik="selectedNIK" :show="showChecklistModal" />
      <GantiPenerima
        :nik="selectedNIK"
        :dbparams="areaParams"
        v-model:show="showGantiPenerima"
        @success="doRebind++"
      />
      <NoRekening
        :nobdt="NoRefs"
        v-model:show="showRekening"
        @success="Rekom(1)"
      />
    </div>
  </Page>
</template>
<script>
import Sidebar from './SideBar.vue'
import DesaGlance from '../DesaGlance.vue'
import ValidasiDetail from '../InputUsulan/ValidasiDetail.vue'
import ProposalDetail from '../InputUsulan/ProposalDetail.vue'
import BerkasDetail from '../InputBerkas/ProposalDetail.vue'
import ChecklistRekom from './Checklist.vue'
import GantiPenerima from './GantiPenerima.vue'
import NoRekening from './NoRekening.vue'

export default {
  components: {
    Sidebar,
    DesaGlance,
    ValidasiDetail,
    ProposalDetail,
    BerkasDetail,
    ChecklistRekom,
    GantiPenerima,
    NoRekening,
  },
  data: () => ({
    priorcolors: ['#333', '#e53935', '#F4511E', '#43A047', '#1E88E5'],
    currentYear: new Date().getFullYear(),
    datagrid: [],
    area: {},
    showDetailModal: false,
    showProposalModal: false,
    showBerkasModal: false,
    showChecklistModal: false,
    showGantiPenerima: false,
    showRekening: false,
    selectedRef: null,
    selectedNIK: null,
    doPrint: 0,
    doRebind: 0,
    rebindSidebar: 0,
    berkasParam: null,
    tahapan: '',

    IsRekom: false,
    NoRefs: ',',
    NIKs: ',',
  }),
  computed: {
    dbref() {
      if (this.area.tabId == 2) {
        return 'PRM.ProposalDet'
      } else {
        return 'PRM.ProposalBSPS'
      }
    },
    rekomOpts() {
      if (!this.tahapan || this.tahapan === 'xxx') return []
      else {
        let i = parseInt(this.tahapan.replace('REKOM ', ''))
        if (i > 1) return [`REKOM ${i - 1}`, `REKOM ${i + 1}`]
        else return [`REKOM ${i + 1}`]
      }
    },
    areaParams() {
      let { Kabupaten, Kecamatan, Kelurahan } = this.area
      return { Kabupaten, Kecamatan, Kelurahan }
    },
    jmlUsulan() {
      return this.datagrid.filter((d) => {
        return d.CheckedValue && (d.FinalStatus || 'OK') == 'OK'
      }).length
    },
    isKabApproved() {
      return this.datagrid.filter((d) => {
        return d.KabKotaApproval
      }).length
    },
    buttonStatus() {
      return this.datagrid.reduce((total, curr) => {
        if (curr.IsSelected && curr.Tahapan) total['IsCancel'] = true
        if (curr.IsSelected && !curr.Tahapan) total['IsRekom'] = true
        return total
      }, {})
    },
  },
  watch: {
    showDetailModal(val) {
      if (!val) this.selectedRef = null
    },
    datagrid() {
      this.SetSelected()
    },
  },
  methods: {
    ClickTambahBaru() {
      this.showDetailModal = true
    },
    SetSelected() {
      this.IsRekom = false
      this.NoRefs = ','
      this.NIKs = ','
      this.tahapan = ''
      this.datagrid.forEach((v) => {
        if (v.IsApproved == 2) this.IsRekom = true
        if (v.IsSelected) {
          if (!this.tahapan) this.tahapan = v.Tahapan
          else if (this.tahapan != v.Tahapan) this.tahapan = 'xxx'

          this.NoRefs += v.NoRef + ','
          this.NIKs += v.NIK + ','
        }
      })
    },
    OpenDetail(noRef) {
      this.selectedRef = noRef
      this.showDetailModal = true
    },
    OpenProposal(nik) {
      this.selectedNIK = nik
      this.showProposalModal = true
    },
    OpenBerkas(KodeDagri, Tahun, Tahapan, NIK) {
      this.berkasParam = {
        NIK,
        KodeDagri,
        Tahun,
        Tahapan,
      }
      this.showBerkasModal = true
    },
    OpenChecklist(nik) {
      this.selectedNIK = nik || this.NIKs
      this.showChecklistModal = true
    },
    OpenGantiPenerima(nik) {
      this.selectedNIK = nik
      this.showGantiPenerima = true
    },
    filterGrid(row) {
      return Boolean(row.IsApproved)
    },
    async Rekom(rekom) {
      // console.log(rekom, this.NoRefs)
      let ret = await this.$api.call('PRM.SavProposalRekom', {
        ProposalID: this.area.ProposalID,
        IsApproved: rekom,
        NoDPA: this.NoRefs,
      })
      this.dbparams.isRekom = { ...this.dbparams, isRekom: rekom }
      if (ret.success) {
        this.IsRekom = rekom
        this.rebindSidebar++
        this.doRebind++
      }
    },
    async GantiRekom(cmd) {
      let ret = await this.$api.call('PRM.SavGantiRekom', {
        ProposalID: this.area.ProposalID,
        NoDPA: this.NoRefs,
        NewRekom: cmd,
      })
      if (ret.success) {
        this.rebindSidebar++
        this.doRebind++
      }
    },
    async OpenRekening(rekom) {
      if (
        !this.isKabApproved &&
        !confirm('Belum disetujui Kabupaten, anda yakin?')
      )
        return

      if (rekom > 0) this.showRekening = true
      else this.Rekom(0)
    },
  },
}
</script>
<style lang="scss">
.page-input-usulan {
  .ui-checkbox {
    .--box.checked {
      color: #1976d2;
    }
  }
}
.mdi-numeric-11-box {
  background-image: url(/img/icons/rk-11.png);
  background-size: contain;
  width: 15px;
  height: 16px;
}
</style>
