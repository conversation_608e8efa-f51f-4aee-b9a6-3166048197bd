<template>
  <Page title="Database Backlog">
    <template #toolbar>
      <v-menu offset-y>
        <template #activator="{ on }">
          <v-btn size="small" color="primary" v-on="on">IMPORT</v-btn>
        </template>
        <v-list dense style="width: 250px">
          <v-list-item @click="OpenImportExcel">
            <template #prepend>
              <v-icon :icon="'mdi-microsoft-excel'"></v-icon>
            </template>
            <v-list-item-content>
              <v-list-item-title>Dari Excel</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
        </v-list>
      </v-menu>
      <v-icon @click="doPrint++">print</v-icon>
    </template>
    <Grid
      id="table-db-blg"
      v-model:datagrid="backlog"
      dbref="BLG.Database"
      :dbparams="filters"
      :disabled="true"
      :height="'calc(100vh - 120px)'"
      :do-rebind="pbdtRebind"
      :columns="[
        {
          name: 'NIK',
          value: 'NIK',
          class: 'plain',
          filter: {
            type: 'search',
            value: 'Keyword',
          },
        },
        {
          name: '',
          value: 'VerStatsID',
          class: 'plain center',
        },
        {
          name: 'Nama',
          value: 'KRT_Nama',
          width: '200px',
          class: 'fix-width',
          filter: {
            type: 'search',
            value: 'Keyword',
          },
        },
        {
          name: 'No. KK',
          value: 'NoKK',
          hide: true,
          filter: {
            type: 'search',
            value: 'NoKK',
          },
        },
        {
          name: 'IDBDT',
          value: 'NoRef',
          hide: true,
        },
        {
          name: 'Alamat',
          value: 'Alamat',
          width: '250px',
          class: 'fix-width',
        },
        {
          name: 'Kabupaten',
          value: 'Kabupaten',
          filter: {
            type: 'select',
            value: 'KabupatenID',
            text: 'Kabupaten',
            dbref: 'Arch.SelArea',
            dbparams: { ParentAreaID: 33 },
          },
        },
        {
          name: 'Kecamatan',
          value: 'Kecamatan',
          filter: {
            type: 'select',
            value: 'KecamatanID',
            text: 'Kecamatan',
            dbref: 'Arch.SelArea',
            dbparams: (f) => ({ ParentAreaID: f.KabupatenID }),
          },
        },
        {
          name: 'Kelurahan',
          value: 'Kelurahan',
          filter: {
            type: 'select',
            value: 'KelurahanID',
            text: 'Kelurahan',
            dbref: 'Arch.SelArea',
            dbparams: (f) => ({ ParentAreaID: f.KecamatanID }),
          },
        },
        {
          name: 'Lahan',
          value: 'StatusTanah',
        },
        {
          name: 'Luas',
          value: 'LuasTanah',
        },
        {
          name: 'DT',
          value: 'NamaData',
        },
        {
          name: 'Intervensi',
          value: 'Intervensi',
        },
      ]"
      :do-print="doPrint"
      @on-print="Print"
    >
      <template #pre-head="{ allfilters, openColumnSetting }">
        <Panel dbref="BLG.SelDescDatabase" :dbparams="allfilters">
          <template #default="{ first }">
            <div style="display: flex" class="pre-head">
              <div style="padding: 8px 12px; flex: 2; font-size: 14px">
                <XInput
                  v-model:value="keyword"
                  right-icon="search"
                  width="230px"
                  placeholder="Cari NIK/Nama .."
                />
              </div>
              <div class="table-desc" style="">
                <v-btn variant="text" size="small" color="gray" @click="openColumnSetting">
                  <v-icon left>mdi-cog</v-icon>
                  ATUR KOLOM
                </v-btn>
                <div style="background-color: #95a5a6">
                  Total: {{ $filters.format(first.Total) }}
                </div>
                <div style="background-color: #2ecc71">
                  Intervensi {{ $filters.format(first.Intervensi) }}
                </div>
                <div style="background-color: #ecf0f1; color: gray">
                  Sisa: {{ $filters.format(first.Sisa) }}
                </div>
              </div>
            </div>
          </template>
        </Panel>
      </template>
      <template #row-NIK="{ row }">
        <v-btn variant="text" size="small" color="primary" @click="OpenDetail(row.NoRef)">
          {{ row.NIK || '&lt; kosong &gt;' }}
        </v-btn>
      </template>
      <template #row-VerStatsID="{ row }">
        <v-icon
          v-if="row.VerStatsID >= 6"
          v-tooltip="'Sudah Terverifikasi'"
          color="primary"
          >mdi-account-check</v-icon
        >
        <v-icon v-if="row.VerStatsID < 6" v-tooltip="'Belum Terverifikasi'">
          mdi-account-question-outline
        </v-icon>
      </template>
    </Grid>
    <ValidasiDetail
      v-model:show="showDetailModal"
      :no-ref="selectedRef"
      :disabled="true"
    />
    <ImportExcel v-model:show="showImportExcel" />
    <ReportViewer v-model:options="reportOptions" :show="showReport" />
  </Page>
</template>
<script>
import ReportViewer from '../../../components/ReportViewer.vue'
import ValidasiDetail from '../ValidasiData/ValidasiDetail.vue'
import ImportExcel from './ImportExcel.vue'

export default {
  components: {
    ValidasiDetail,
    ReportViewer,
    ImportExcel,
  },
  data: () => ({
    showDetailModal: false,
    showImportExcel: false,
    filters: {},
    selectedRef: null,
    backlog: [],
    reportUrl: null,
    showReport: false,
    doPrint: 0,
    reportOptions: {},
    keyword: '',
    pbdtRebind: 1,
  }),
  watch: {
    keyword(val) {
      this.filters.Keyword = val
      this.pbdtRebind++
    },
  },
  async mounted() {},
  methods: {
    OpenDetail(noRef) {
      this.selectedRef = noRef
      this.showDetailModal = true
    },
    OpenImportExcel() {
      this.showImportExcel = true
    },
    Print(headers, data) {
      this.reportOptions = {
        headers: headers,
        data: data,
      }
      this.showReport = true
    },
  },
}
</script>
<style lang="scss">
.table-desc {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;

  div {
    padding: 3px 5px;
    margin-left: 3px;
    color: white;
  }
}
</style>
