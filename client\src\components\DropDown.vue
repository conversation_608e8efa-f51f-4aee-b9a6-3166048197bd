<template>
  <div style="display: inline-block">
    <v-menu offset-y>
      <template #activator="{ on }">
        <v-btn v-bind="$attrs" v-on="on">
          <slot></slot>
          <v-icon right>expand_more</v-icon>
        </v-btn>
      </template>
      <v-list dense>
        <v-list-item
          v-for="(item, idx) in items"
          :key="idx"
          @click="item.click"
        >
          <template #prepend>
            <v-icon :icon="item.icon"></v-icon>
          </template>
          <v-list-item-content>
            <v-list-item-title>{{ item.text }}</v-list-item-title>
          </v-list-item-content>
        </v-list-item>
      </v-list>
    </v-menu>
  </div>
</template>
<script>
export default {
  props: {
    items: Array,
  },
}
</script>
