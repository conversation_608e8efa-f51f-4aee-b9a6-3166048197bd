var db = require('../../common/db')
var moment = require('moment')
var Excel = require('exceljs')

const Header = {
  colswidth: {
    No: 6,
    NIK: 17,
    Nama: 24,
    KRT_Nama: 24,
    Alamat: 50,
    Kabupaten: 15,
    Kecamatan: 15,
    <PERSON><PERSON><PERSON><PERSON>: 15,
    <PERSON><PERSON>: 15,
  },
  GenerateGeneric(sheet, keys) {
    let s_header = {
      border: {
        top: {style: 'thin'},
        left: {style: 'thin'},
        bottom: {style: 'thin'},
        right: {style: 'thin'},
      },
      font: {size: 10, bold: true},
      alignment: {vertical: 'middle', horizontal: 'center'},
    }
    // Generating Dynamic Headers
    for (let i = 0; i < keys.length; i++) {
      let colspan = 1
      let j = i
      let c1 = '',
        c2 = ''
      do {
        if (j + 1 >= keys.length) break
        c1 = keys[j].split('_', 2)
        c2 = keys[j + 1].split('_', 2)
        if (c1[0] == c2[0]) colspan++
        j++
      } while (c1[0] == c2[0])

      if (colspan == 1) {
        sheet.mergeCells(1, i + 1, 2, i + 1)
        sheet.getCell(1, i + 1).value = keys[i].replace(/_/g, ' ')
        // console.log(keys[i].replace(/_/g, ' '))
        sheet.getColumn(i + 1).width =
          this.colswidth[keys[i]] || Math.ceil(keys[i].length * 1.5)
        sheet.getCell(1, i + 1).border = s_header.border
        sheet.getCell(2, i + 1).alignment = s_header.alignment
      } else {
        // // array_push(hcol, ['value'=>c1[0], 'colspan'=>colspan]);
        sheet.mergeCells(1, i + 1, 1, i + colspan)
        sheet.getCell(1, i + 1).value = c1[0].replace(/_/g, ' ')
        // console.log(c1[0].replace(/_/g, ' '))
        sheet.getCell(1, i + 1).border = s_header.border
        sheet.getCell(1, i + 1).alignment = s_header.alignment
        let len = colspan + i
        while (i < len) {
          let c2 = keys[i].split('_', 2)
          sheet.getCell(2, i + 1).value = c2[1].replace(/_/g, ' ')
          // console.log(c2[1].replace(/_/g, ' '))
          sheet.getCell(2, i + 1).border = s_header.border
          sheet.getCell(2, i + 1).alignment = s_header.alignment

          i++
        }
        i--
      }
    }
    sheet.getRow(2).font = s_header.font
    sheet.getRow(1).font = s_header.font
  },
  renderHeaderCell(sheet, r, c, h, d) {
    let text = h.text
    if (typeof h.text == 'function') {
      text = h.text(d)
    }

    sheet.getCell(r, c).value = text
    if (h.width) sheet.getColumn(c).width = h.width
    if (h.colspan || h.rowspan) {
      sheet.mergeCells(r, c, r + (h.rowspan || 0), c + (h.colspan || 0))
      sheet.getCell(r, c).alignment = {
        vertical: 'middle',
        horizontal: 'center',
      }
    }
    if (h.border) sheet.getCell(r, c).border = h.border
    sheet.getCell(r, c).alignment = h.alignment || {
      vertical: 'middle',
      horizontal: 'center',
    }
    sheet.getCell(r, c).font = {bold: true, ...(h.font || {})}
  },
  Render(sheet, structure, row) {
    if (structure.header === undefined)
      this.GenerateGeneric(sheet, Object.keys(row))
    else if (structure.header) {
      let r = 1
      structure.header.forEach((h, i) => {
        if (typeof h == 'string') {
          sheet.getCell(r + i, 1).value = h
        } else if (typeof h == 'object' && h !== null) {
          if (!h.forEach) this.renderHeaderCell(sheet, r, 1, h, row)
          else
            h.forEach((hh, c) => {
              this.renderHeaderCell(sheet, r, c + 1, hh, row)
            })
        }
        r++
      })
    }
  },
}
class Body {
  constructor() {
    this.lastgroup = ''
    this.currentRow = 0
    this.idx = 1
    this.idxgrp = 1
    this.startGroupRow = 0
  }
  GererateGeneric(sheet, d, r) {
    let keys = Object.keys(d)
    for (let i = 0; i < keys.length; i++) {
      sheet.getCell(r, i + 1).value = d[keys[i]]
    }
  }
  RenderGroupHeaderRow(sheet, g, d, r) {
    if (!g.header) sheet.getCell(r, 1).value = d[g.key]
    else if (g.header.forEach) {
      g.header.forEach(hr => {
        this.RenderCell(sheet, hr, d, 1)
        this.currentRow++
      })
    }
  }
  RenderGroupFooterRow(sheet, g, d, r, addtRow) {
    if (g && g.footer && g.footer.forEach) {
      if(addtRow) this.currentRow += addtRow
      g.footer.forEach(hr => {
        this.RenderCell(sheet, hr, d, 1)
        this.currentRow++
      })
    }
  }
  RenderCell(sheet, o, d, c) {
    if (typeof o == 'string') {
      sheet.getCell(this.currentRow, 1).value = o
    } else if (typeof o == 'object') {
      if (o === null) {
        // do-nothing
      } else if (o.forEach) {
        // array of row
        let currcol = 1
        o.forEach(rx => {
          this.RenderCell(sheet, rx, d, currcol)
          currcol += rx.colspan ? rx.colspan + 1 : 0 + 1
        })
      } else {
        if (o.key) sheet.getCell(this.currentRow, c).value = d[o.key]
        else if (typeof o.text == 'function')
          sheet.getCell(this.currentRow, c).value = o.text({
            ...d,
            idx: this.idx,
            idxgrp: this.idxgrp,
          })
        else sheet.getCell(this.currentRow, c).value = o.text
        if (o.fn === 'SUM') {
          let col = String.fromCharCode(64 + c)
          sheet.getCell(this.currentRow, c).value = {
            formula: `SUM(${col}${this.startGroupRow}:${col}${this.currentRow -
              1})`,
          }
        }

        if (o.colspan || o.rowspan) {
          sheet.mergeCells(
            this.currentRow,
            c,
            this.currentRow + (o.rowspan || 0),
            c + (o.colspan || 0)
          )
          sheet.getCell(this.currentRow, c).alignment = {
            vertical: 'middle',
            horizontal: 'center',
          }
          this.currentRow += o.rowspan || 0
        }
        if (o.border) sheet.getCell(this.currentRow, c).border = o.border
        if (o.alignment)
          sheet.getCell(this.currentRow, c).alignment = o.alignment
        if (o.font) sheet.getCell(this.currentRow, c).font = o.font
        if (o.width) sheet.getColumn(c).width = o.width
      }
    }
  }
  Render(sheet, structure, d, r) {
    this.currentRow = r
    if (structure.grouprow) {
      if (d[structure.grouprow.key] !== this.lastgroup) {
        if (this.lastgroup)
          this.RenderGroupFooterRow(sheet, structure.grouprow, d, r)
        this.RenderGroupHeaderRow(sheet, structure.grouprow, d, r)
        this.lastgroup = d[structure.grouprow.key]
        this.currentRow++
        this.startGroupRow = this.currentRow
        this.idxgrp = 1
      }
    }
    if (structure.body === undefined)
      this.GererateGeneric(sheet, d, this.currentRow)
    else this.RenderCell(sheet, structure.body, d)
    this.idx++
    this.idxgrp++
    return this.currentRow
  }
}
module.exports = {
  BORDER_THIN: {
    top: {style: 'thin'},
    left: {style: 'thin'},
    bottom: {style: 'thin'},
    right: {style: 'thin'},
  },
  ALIGN_CENTER: {vertical: 'middle', horizontal: 'center'},
  ALIGN_LEFT: {vertical: 'middle', horizontal: 'left'},
  ALIGN_RIGHT: {vertical: 'middle', horizontal: 'right'},
  FONT_BOLD: {bold: true},
  template(strings, ...keys) {
    return function(...values) {
      let dict = values[values.length - 1] || {}
      let result = [strings[0]]
      keys.forEach(function(key, i) {
        let value = Number.isInteger(key) ? values[key] : dict[key]
        result.push(value, strings[i + 1])
      })
      return result.join('')
    }
  },
  async Render(structure, params) {
    let dd = moment().format('mmss')
    let filename =
      (structure.name || 'Report').replace(/[^a-z0-9]/gi, '_') + '_' + dd
    let workbook = new Excel.stream.xlsx.WorkbookWriter({
      filename: `tmp/${filename}.xlsx`,
      useStyles: true,
      useSharedStrings: true,
    })

    if (structure.forEach) {
      for (let i = 0; i < structure.length; i++) {
        structure[i].params = {...structure[i].params, ...params}
        await this.RenderStructure(workbook, structure[i])
      }
    } else {
      structure.params = {...structure.params, ...params}
      this.RenderStructure(workbook, structure)
    }

    await workbook.commit()
    return filename
  },
  async RenderStructure(workbook, structure) {
    let xbody = null
    let data = await db.exec(structure.sp, structure.params)
    if (structure.groupsheet) {
      let lastgroup = ''
      let sheet = null
      let r = 1
      for (let i = 0; i < data.length; i++) {
        let d = data[i]
        // Render Header
        if (lastgroup != d[structure.groupsheet.key]) {
          if (xbody) {
            xbody.RenderGroupFooterRow(
              sheet,
              structure.grouprow,
              data[i - 1],
              r,
              1
            )
          }
          let sheetname = structure.groupsheet.text
            ? typeof structure.groupsheet.text == 'function'
              ? structure.groupsheet.text(d)
              : structure.groupsheet.text
            : d[structure.groupsheet.key]
          sheet = workbook.addWorksheet(sheetname)
          this.renderHeader(sheet, structure, d)
          lastgroup = d[structure.groupsheet.key]
          r = sheet.lastRow ? sheet.lastRow._number : 0 + 1
          r++
          xbody = new Body()
        }

        // Render Body
        // console.log(d);
        r = xbody.Render(sheet, structure, d, r)
        r++

        // Render Last Footer
        if (i + 1 >= data.length && structure.grouprow) {
          xbody.RenderGroupFooterRow(sheet, structure.grouprow, d, r, 1)
        }
      }
    } else {
      let sheet = workbook.addWorksheet('Report')
      this.renderHeader(sheet, structure, data[0])
      let r = sheet.lastRow._number + 1
      for (let i = 0; i < data.length; i++) {
        let d = data[i]
        r = xbody.Render(sheet, structure, d, r)
        r++
      }
      //   this.renderFooter(structure.footer);
    }
  },
  renderHeader(sheet, structure, row) {
    Header.Render(sheet, structure, row)
  },
  renderBody(sheet, structure, d, r) {
    Body.Render(sheet, structure, d, r)
  },
}
