<template>
  <div style="padding:10px;">
    <Uploader>
      <template v-slot:drop-area>
        <div>Droper</div>
      </template>
      <template v-slot:file-opener="{ opener }">
        <div @click="opener">test dunk</div>
      </template>
    </Uploader>
    <Grid :datagrid="datatable" style="width:500px">
      <template v-slot:header>
        <row>
          <hcol sm>
            Head 1
          </hcol>
          <hcol md>
            Head 2
          </hcol>
          <hcol sm>
            Head 3
          </hcol>
        </row>
      </template>
      <template v-slot:content="{ item }">
        <xcol sm>
          {{ item.col1 }}
        </xcol>
        <xcol md>
          {{ item.col2 }}
        </xcol>
        <xcol sm>
          {{ item.col3 }}
        </xcol>
      </template>
      <template v-slot:insert="{ item }">
        <xcol sm>
          <XInput v-model:value="item.col1" />
        </xcol>
        <xcol md>
          {{ item.col2 }}
        </xcol>
      </template>
    </Grid>
  </div>
</template>

<script>
//import Vue from "vue";
import Uploader from '@/components/Uploader'
import Input from '@/components/Forms/Input'
import { Grid, hcol, row, xcol } from '../../components/Grid'
import 'material-design-icons-iconfont/dist/material-design-icons.css'

export default {
  components: {
    Uploader,
    Input,
    Grid,
    hcol,
    row,
    xcol,
  },
  data: () => ({
    text: 'asd',
    files: [],
    datatable: [
      { col1: '#1 Column 1', col2: '#1 Column 2', col3: '#1 Column 3' },
      { col1: '#2 Column 1', col2: '#2 Column 2', col3: '#2 Column 3' },
      { col1: '#3 Column 1', col2: '#3 Column 2', col3: '#3 Column 3' },
    ],
  }),
  methods: {},
}
</script>
