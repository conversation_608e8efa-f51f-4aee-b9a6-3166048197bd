<template>
  <Modal
    title="DETAIL MONITORING"
    v-model:show="xshow"
    width="1000px"
    @onSubmit="Save"
  >
    <div class="form-inline" style="width: 780px">
      <div class="iblock" style="width: 300px">
        <div style="padding: 5px">
          <Uploader
            style="
              width: 80px;
              height: 80px;
              display: inline-block;
              margin-right: 8px;
            "
            v-model:value="forms.Profile"
            accept=".jpg,.jpeg,.jfif,.png,.heic"
          ></Uploader>
          <div class="iblock">
            <div style="font-size: small">{{ forms.NIK }}</div>
            <div>{{ forms.Nama }}</div>
            <div
              style="
                font-size: small;
                color: gray;
                width: 200px;
                height: 40px;
                overflow: hidden;
              "
            >
              {{ forms.Alamat }}
            </div>
          </div>
        </div>
        <div class="iblock dv-kerusakan" style="width: 100%">
          <div style="padding: 0px; font-weight: bold">TAMBAHAN SWADAYA</div>
          <div class="">
            <!-- <XSelect
              :items="[
                { key: '0', value: 'Tanpa Swadaya' },
                { key: '1', value: 'Uang' },
                { key: '2', value: 'Bahan Bangunan' },
                { key: '9', value: 'Lain-Lain' },
              ]"
              v-model:value="forms.SwadayaID"
              style="width:120px"
              width="120px"
            /> -->
            <XInput
              label="Uang"
              type="number"
              placeholder="Sebesar.."
              v-model:value="forms.SwadayaUang"
            />
            <XInput
              label="Material"
              type="number"
              placeholder="Sebesar.."
              v-model:value="forms.SwadayaMaterial"
            />
            <XInput
              label="Tenaga/Tukang"
              type="number"
              placeholder="Sebesar.."
              v-model:value="forms.SwadayaTenaga"
            />
            <XInput
              label="Lainnya"
              type="number"
              placeholder="Sebesar.."
              v-model:value="forms.SwadayaLainnya"
            />
          </div>
        </div>
      </div>
      <div class="iblock" style="width: 460px; margin-left: 15px">
        <div style="padding: 0px 5px">
          <Map
            v-model:lat="forms.GeoLat"
            v-model:lon="forms.GeoLng"
            width="450px"
            height="230px"
            :searchbox="true"
          />
        </div>
        <div style="padding: 0 5px">
          <div
            style="
              padding: 10px 0;
              background: #f3f3f3;
              width: 100%;
              display: flex;
              font-size: small;
            "
          >
            <div class="iblock" style="width: 140px; padding-left: 10px">
              <i class="fa fa-user"></i>&nbsp;&nbsp;LAHAN
            </div>
            <div class="iblock" style="flex: 1; padding-left: 10px">
              <i class="fa fa-home"></i>&nbsp;&nbsp;PONDASI
            </div>
            <div class="iblock" style="flex: 1; padding-left: 10px">
              <i class="fa fa-home"></i>&nbsp;&nbsp;RUSPIN
            </div>
          </div>
          <div style="display: flex">
            <Uploader
              v-model:value="forms.Rumah0"
              accept=".jpg,.jpeg,.jfif,.png,.heic"
            ></Uploader>
            <Uploader
              v-model:value="forms.Lantai0"
              accept=".jpg,.jpeg,.jfif,.png,.heic"
            ></Uploader>
            <Uploader
              v-model:value="forms.Dinding0"
              accept=".jpg,.jpeg,.jfif,.png,.heic"
            ></Uploader>
          </div>
        </div>
        <div style="padding: 0 5px">
          <div
            style="
              padding: 10px 0;
              background: #f3f3f3;
              width: 100%;
              display: flex;
              font-size: small;
            "
          >
            <div class="iblock" style="width: 140px; padding-left: 10px">
              <i class="fa fa-user"></i>&nbsp;&nbsp;TAMPAK DEPAN
            </div>
            <div class="iblock" style="flex: 1; padding-left: 10px">
              <i class="fa fa-home"></i>&nbsp;&nbsp;TAMPAK SAMPING
            </div>
            <div class="iblock" style="flex: 1; padding-left: 10px">
              <i class="fa fa-home"></i>&nbsp;&nbsp;DALAM RUMAH
            </div>
          </div>
          <div style="display: flex">
            <Uploader
              v-model:value="forms.RumahDepan"
              accept=".jpg,.jpeg,.jfif,.png,.heic"
            ></Uploader>
            <Uploader
              v-model:value="forms.RumahSamping"
              accept=".jpg,.jpeg,.jfif,.png,.heic"
            ></Uploader>
            <Uploader
              v-model:value="forms.Denah"
              accept=".jpg,.jpeg,.jfif,.png,.heic"
            ></Uploader>
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    xshow: false,
    forms: {},
    checkAll: false,
  }),
  props: {
    show: Boolean,
    nik: [String, Number],
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      if (!val) this.forms = {}
      else if (this.xshow) this.populate()
      this.$emit('update:show', val)
    },
    checkAll(val) {
      for (let x in this.forms) {
        if (x.match(/^Cek/)) this.forms[x] = val
      }
    },
  },
  methods: {
    async populate() {
      let { data } = await this.$api.call('BCN.SelMonev', {
        NIK: this.nik,
      })
      this.forms = data[0]
    },
    async Save() {
      let ret = await this.$api.call('BCN.SavMonev', {
        ...this.forms,
        NIK: this.nik,
      })
      if (ret.success) this.$emit('update:show', false)
    },
  },
}
</script>
<style lang="scss">
.modal-detail-monitoring {
  .inline-form {
    .form-coms.ui-checkbox {
      display: flex;
      .form-label {
        width: 140px;
      }
    }
  }
}
</style>
