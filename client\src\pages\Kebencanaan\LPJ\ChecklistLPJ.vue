<template>
  <Modal
    title="CHECKLIST LPJ"
    v-model:show="xshow"
    width="400px"
    @onSubmit="Save"
  >
    <div style="display: flex" class="form-inline">
      <div class="iblock" style="width: 350px">
        <div style="padding: 5px">
          <CheckUpload
            label="Cover LPJ"
            accept=".pdf"
            v-model:value="forms.CoverLPJ"
          />
          <CheckUpload
            label="Surat Laporan Bansos"
            v-model:value="forms.LapBansos"
          />
          <CheckUpload
            label="Laporan TPK"
            accept=".pdf"
            v-model:value="forms.LapTPK"
          />
          <CheckUpload
            label="Surat Pernyataan Tanggung Jawab"
            accept=".pdf"
            v-model:value="forms.SuratTggJwb"
          />
          <CheckUpload
            label="Daftar Hadir"
            accept=".pdf"
            style="width: 300px"
            v-model:value="forms.DaftarHadir"
          />
          <CheckUpload
            label="<PERSON><PERSON><PERSON>"
            accept=".pdf"
            v-model:value="forms.BuktiUangKeluar"
          />
          <CheckUpload
            label="<PERSON>ukti <PERSON>"
            accept=".pdf"
            v-model:value="forms.BuktiPajak"
          />
          <!-- <CheckUpload
            label="FC Rekening Desa"
            accept=".pdf"
            v-model:value="forms.FCRekDesa"
          />
          <CheckUpload
            label="Berita Acara Pencairan"
            accept=".pdf"
            v-model:value="forms.BAPPencairan"
          /> -->
          <!-- <CheckUpload
            label="Realisasi Penggunaan Dana"
            accept=".pdf"
            v-model:value="forms.RealisasiDana"
          /> -->
          <!-- <CheckUpload
            label="Foto Kegiatan"
            accept=".pdf"
            v-model:value="forms.FotoKegiatan"
          /> -->
        </div>
      </div>
    </div>
    <!-- <template v-slot:left-action>
      <div style="padding-left: 10px; color: gray">
        <Checkbox text="Check Semua" v-model:value="checkAll" />
      </div>
    </template> -->
  </Modal>
</template>
<script>
import CheckUpload from '../Pencairan/CheckUpload.vue'
export default {
  components: {
    CheckUpload,
  },
  data: () => ({
    xshow: false,
    forms: {},
    checkAll: false,
  }),
  props: {
    show: Boolean,
    nik: [String, Number],
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      this.$emit('update:show', val)
    },
    nik(val) {
      if (val) this.populate()
    },
    checkAll(val) {
      for (let x in this.forms) {
        if (x !== 'NIK') this.forms[x] = val
      }
    },
  },
  methods: {
    async populate() {
      let { data } = await this.$api.call('BCN.SelMonevCheck', {
        NIK: this.nik,
      })
      if (data && data.length) this.forms = data[0]
      else this.forms = {}
    },
    async Save() {
      let ret = await this.$api.call('BCN.SavMonevCheck', {
        ...this.forms,
        NIK: this.nik,
      })
      if (ret.success) this.$emit('update:show', false)
    },
  },
}
</script>
<style lang="scss">
.modal-proposal {
  .v-dialog--content {
    padding: 0;
  }
}
</style>
