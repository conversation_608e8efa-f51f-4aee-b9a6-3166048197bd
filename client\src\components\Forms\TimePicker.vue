<template>
  <v-menu
    ref="menu"
    v-model="showPopup"
    v-model:return-value="time"
    :close-on-content-click="false"
    transition="scale-transition"
    offset-y
    max-width="290px"
    min-width="290px"
  >
    <template #activator="{ on }">
      <XInput v-model="time" :label="label" type="text" v-on="on" />
    </template>
    <v-time-picker
      v-if="showPopup"
      v-model="time"
      full-width
      @click:minute="$refs.menu.save(time)"
    ></v-time-picker>
  </v-menu>
</template>
<script>
export default {
  props: {
    label: String,
    value: [String, Object],
  },
  data: () => ({
    showPopup: false,
    time: null,
  }),
}
</script>
