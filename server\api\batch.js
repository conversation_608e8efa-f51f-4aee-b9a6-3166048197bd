var express = require("express");
var router = express.Router();
var db = require("../common/db");
var ertlh = require("./thirdparty/ertlh");

const xmethods = {
  async import(kd) {
    let kab = [];
    if (kd == "kota") {
      kab = await db.query(`SELECT Kabupaten, Kecamatan , Kelurahan , KodeDagri FROM arch_varea av 
              WHERE Kabupaten LIKE 'SUKOHARJO'
              ORDER BY 4;`);
    } else {
      kab = await db.query(`SELECT Kabupaten, Kecamatan , Kelurahan , KodeDagri FROM arch_varea av 
              WHERE KodeDagri = ${kd};`);
    }
    for (let i = 0; i < kab.length; i++) {
      console.log(kab[i]);
      let data = await ertlh.read(kab[i].KodeDagri);
      await db.query(`DELETE FROM raw_ertlh WHERE kode_wilayah = ${kd}`);
      let sqlinsert = "INSERT IGNORE INTO raw_ertlh (";
      if (data.length && data.forEach) {
        sqlinsert += Object.keys(data[0]).join(",") + ") VALUES ";
        data.forEach(d => {
          sqlinsert += (
            "('" +
            Object.values(d)
              .join("|")
              .replace(/'/g, "\\'")
              .replace(/\|/g, "','") +
            "'),"
          ).replace(/''/g, "NULL");
        });
      }
      await db.query(sqlinsert.replace(/,$/, ";"));
    }
  },
  async export(kd, withCheck) {
    let kab = [];
    let sql = "SELECT 1";
    if (kd == "test") {
      sql = `SELECT p.NIK, p.Kabupaten, p.KodeWilayah FROM prm_pbdt p
          LEFT JOIN raw_ertlh e
          ON p.NIK = e.NIK
        WHERE p.Kabupaten = 'CILACAP' AND e.nik IS NULL AND p.SkorKelayakan < 500 AND p.NIK IS NOT NULL AND p.VerStatsID = 6`;
    } else if (kd == "bsps") {
      sql = `SELECT p.NIK, p.Kabupaten, p.KodeWilayah FROM prm_proposaldet p
              JOIN prm_pbdt d
              ON p.NIK = d.NIK 
              LEFT JOIN raw_ertlh e
              ON p.NIK = e.NIK
            WHERE p.Sumber = 1 AND p.Tahun = YEAR(NOW()) AND e.NIK IS NULL AND d.SkorKelayakan < 500
            ORDER BY p.KodeWilayah;`;
    } else { 
      sql = `SELECT p.NIK, p.Kabupaten, p.KodeWilayah FROM prm_pbdt p
              LEFT JOIN raw_ertlh e
              ON p.NIK = e.NIK
              WHERE p.KodeWilayah = ${kd} AND p.VerStatsID = 6 AND e.NIK IS NULL AND SkorKelayakan < 500;`;
    }





    kab = await db.query(sql);
    let lastKab = "";
    let err = [];
    for (let i = 0; i < kab.length; i++) {
      if (lastKab != kab[i].KodeWilayah) {
        if (lastKab !== "") {
          if (withCheck) {
            xmethods.check(sql, lastKab);
            // err = await xmethods.check(sql, lastKab);
            // if (err.length > 0) break;
          }
        }
        console.log(kab[i].Kabupaten, kab[i].KodeWilayah);
        lastKab = kab[i].KodeWilayah;
      }
      console.log(`${i}/${kab.length}`, kab[i].NIK);
      await ertlh.insert(kab[i].NIK);
    }
    if (withCheck && !err.length) {
      await xmethods.check(sql, lastKab);
    }
  },
  async check(sql, kd) {
    console.log(`Checking ${kd} ...`);
    await xmethods.import(kd);
    let kab = await db.query(sql);
    if (kd) kab = kab.filter(k => k.KodeWilayah == kd);
    console.log(kab.length);
    return kab;
  }
};

router.get("/ertlh/import/:kd", async (req, res) => {
  await xmethods.import(req.params.kd);
  res.send("ok");
});

router.get("/ertlh/export/:kd", async (req, res) => {
  await xmethods.export(req.params.kd, true);
  res.send("ok");
});

module.exports = router;
