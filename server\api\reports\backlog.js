const express = require('express')
const router = express.Router()
const moment = require('moment')
const carbone = require('carbone')
var db = require('../../common/db')

router.get('/doc/:filename', async function(req, res) {
    
  res.header('Cache-Control', 'private, no-cache, no-store, must-revalidate')
  res.header('Expires', '-1')
  res.header('Pragma', 'no-cache')
  // Data to inject
  const {filename} = req.params  
  let {out, sp} = req.query  

  const tipe = filename.replace(/(.ods|.xlsx|.docx)$/,'')
  if (!sp) {
    sp = 'BLG_RptDoc'+tipe
  }
  let d = await db.exec(sp, req.query)
  
  if (!d.length) {
    res.send({
      success: false,
      message: 'data tidak ditemukan'
    })
    return
  }
  let data = d
  if (d.length == 1) {
    data = d[0]
    for(let k in data) {
      if (k.match(/^Tgl/) && data[k]) {
        data[k] = moment(data[k]).add(1, 'days')
      }
    }
  }
  console.log(data)
  
  let opts = {}
  if (out == 'pdf') {
    res.header('Content-Type', 'application/pdf')
    opts = {...opts, convertTo: 'pdf'}
  } else if (filename.match(/.ods$/)) {
    res.header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    opts = {...opts, convertTo: 'xlsx'}
  }
  
  carbone.render(
    `./tmp/templates/backlog/${filename}`,
    data, opts,
    function (err, result) {
      if (err) return console.log(err)

      res.send(result)
    }
  )
    
})

module.exports = router