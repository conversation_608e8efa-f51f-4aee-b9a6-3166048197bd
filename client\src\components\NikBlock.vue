<template>
  <div
    style="letter-spacing: 1.1px"
    :style="{ cursor: isClicked ? '' : 'pointer' }"
    @click="isClicked = true"
  >
    {{
      isClicked ? nik : nik?.replace(/^(.{3})(.{11})/, '$1 * * * * * * * * * ')
    }}
  </div>
</template>
<script>
export default {
  props: {
    nik: {
      type: String,
      default: null,
    },
  },
  data: () => ({
    isClicked: false,
  }),
  watch: {
    nik() {
      this.isClicked = false
    },
  },
}
</script>
<style lang="scss"></style>
