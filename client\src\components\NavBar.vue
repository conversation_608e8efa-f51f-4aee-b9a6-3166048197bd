<template>
  <section class="nav-bar">
    <div class="nav-container">
      <div class="brand">
        <v-icon
          v-if="isMobile && isFocused"
          large
          color="white"
          style="margin-left: -30px; margin-top: -23px; z-index: 10"
          @click="MobileBack"
        >
          mdi-arrow-left
        </v-icon>
        <a v-else href="/">
          <img src="/imgs/logo.png" />
        </a>
      </div>
      <nav>
        <div class="nav-mobile">
          <a id="nav-toggle" @click="openMenu">
            <v-icon large color="white">mdi-menu</v-icon>
          </a>
        </div>
        <ul v-show="menuMobileOpen" class="nav-list">
          <li
            v-for="(m, idx) in menu"
            v-show="m.MenuUrl !== 'component'"
            :key="idx"
          >
            <a href="javascript:void(0)" @click="handleMenuClick(m.MenuUrl)">
              <!-- <v-badge color="red" content="3">
              </v-badge> -->
              {{ m.MenuName }}
            </a>
            <ul v-if="m.child" class="nav-dropdown">
              <li
                v-for="(sm, sidx) in m.child"
                v-show="m.MenuUrl !== 'component'"
                :key="sidx"
              >
                <a
                  href="javascript:void(0)"
                  @click="handleMenuClick(sm.MenuUrl)"
                >
                  {{ sm.MenuName }}
                </a>
              </li>
            </ul>
          </li>
        </ul>
      </nav>
      <div v-if="menu.length" id="bell_div">
        <v-badge
          :content="newNotifCount"
          :value="newNotifCount"
          :style="{ cursor: notifications.length ? 'pointer' : 'initial' }"
          color="red"
          overlap
        >
          <v-icon id="bell" color="white"> mdi-bell-outline </v-icon>
        </v-badge>
        <Popover target-id="bell" on="click" @click="OpenNotification">
          <template #default>
            <!-- <MessageList
              :items="messages"
              :disabled="true"
              @click="MessageClick"
            /> -->
            <v-card style="max-height: 360px; overflow: auto">
              <v-list-item
                v-for="(notif, idx) in notifications"
                :key="idx"
                three-line
                @click="handleNotifClick(notif)"
              >
                <v-list-item-title style="display: flex">
                  <v-icon
                    v-if="notif.isNew"
                    color="success"
                    x-small
                    style="margin: -3px 5px 0 0"
                  >
                    mdi-circle
                  </v-icon>
                  <div>
                    {{
                      notif.NotifType
                        ? notif.NotifType.substring(4)
                            .replaceAll('-', ' ')
                            .toUpperCase()
                        : ''
                    }}
                  </div>
                  <v-spacer />
                  <div style="color: silver">
                    {{ $filters.format(notif.CreatedAt, 'fromNow') }}
                  </div>
                </v-list-item-title>
                <v-list-item-subtitle>
                  {{ notif.Description }}
                </v-list-item-subtitle>
              </v-list-item>
            </v-card>
            <div v-if="!notificationEnabled" @click="RegisterNotification">
              <v-btn
                text
                color="warning"
                style="width: 100%"
                @click="MarkAllAsRead"
              >
                IJINKAN NOTIFIKASI
              </v-btn>
            </div>
            <div>
              <v-btn
                text
                color="primary"
                style="width: 100%"
                @click="MarkAllAsRead"
              >
                TELAH DIBACA SEMUA
              </v-btn>
            </div>
          </template>
        </Popover>
      </div>
      <div v-if="user" id="name_div">
        {{ user ? user.FullName : '' }}
      </div>
    </div>
  </section>
</template>
<script>
import { mapActions, mapGetters } from 'vuex'
import Popover from './Forms/Popover.vue'
// import MessageList from '../pages/RTLH/ReviewUsulan/MessagesList.vue'

export default {
  components: {
    Popover,
    // MessageList,
  },
  data: () => ({
    notifications: [],
    newNotifCount: 0,
    menuMobileOpen: false,
    notificationEnabled: false,
  }),
  computed: {
    ...mapGetters({
      menu: 'getMenu',
      user: 'getUser',
      isFocused: 'isPageFocused',
    }),
    reactiveNotifications() {
      return this.notifications
    },
  },
  created() {
    if (window.Notification && window.Notification.permission == 'granted') {
      this.notificationEnabled = true
    }
    this.$api.addEventListener('notification', (n) => {
      this.notifications.splice(0, 0, n)
      this.newNotifCount++

      if (window.Notification && window.Notification.permission == 'granted') {
        const nw = new window.Notification(
          n.NotifType
            ? n.NotifType.substring(4).replaceAll('-', ' ').toUpperCase()
            : '',
          {
            body: n.Description,
          }
        )
        setTimeout(() => {
          nw.close()
        }, 5000)
      }
    })
  },
  async mounted() {
    // setInterval(this.populateMessages, 360000)
    this.populateMessages()
  },
  methods: {
    ...mapActions(['setPageFocused']),
    async RegisterNotification() {
      try {
        if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
          alert('Push notifications are not supported in your browser')
          return
        }

        if (window.Notification?.permission === 'denied') {
          alert(
            'Klik icon lonceng pada browser url ujung sebelah kanan, dan pilih Ijinkan'
          )
          return
        }

        const permission = await Notification.requestPermission()
        this.notificationEnabled = permission === 'granted'

        if (permission === 'granted') {
          const registration = await navigator.serviceWorker.ready
          const subscription = await registration.pushManager.subscribe({
            userVisibleOnly: true,
            applicationServerKey: this.urlBase64ToUint8Array(
              'BHNlq0xbaervKOcZHEFR8lNa8Z1F72uB5lsbZf9NyLtI7AGF2d9nwNPiFSrNqkwDneL8UpBsZKcJ7pQeNKTXUE0'
            ),
          })

          await this.$api.post('/api/notifications/subscribe', {
            subscription,
            userId: this.user?.UserID,
          })
        }
      } catch (error) {
        console.error('Error enabling notifications:', error)
        alert('Failed to enable notifications: ' + error.message)
      }
    },

    urlBase64ToUint8Array(base64String) {
      const padding = '='.repeat((4 - (base64String.length % 4)) % 4)
      const base64 = (base64String + padding)
        .replace(/-/g, '+')
        .replace(/_/g, '/')
      const rawData = window.atob(base64)
      const outputArray = new Uint8Array(rawData.length)
      for (let i = 0; i < rawData.length; ++i) {
        outputArray[i] = rawData.charCodeAt(i)
      }
      return outputArray
    },
    async populateMessages() {
      let { data } = await this.$api.call('Arch_SelNotifications', {
        nocache: true,
      })
      let lastId = localStorage.getItem('last-notif-id') || 0
      this.newNotifCount = 0
      this.notifications = data.map((d) => {
        if (d.id > lastId) this.newNotifCount++
        return { ...d, isNew: d.id > lastId }
      })
    },
    handleMenuClick(url) {
      // evt.target.closest("LI").querySelector("UL").style.display = "initial";
      if (url.match(/^javascript:/)) eval(url)
      else if (url) {
        this.$router.push(url)
        // document.querySelector('.nav-list').style.display = 'none'
        this.menuMobileOpen = false
      }
    },
    handleNotifClick(n) {
      n.isNew = 0
      if (n.Url.match(/^javascript:/)) eval(n.Url)
      else if (n.Url) {
        window.location = n.Url
        // document.querySelector('.nav-list').style.display = 'none'
        this.menuMobileOpen = false
      }
    },
    MarkAllAsRead() {
      for (let n of this.notifications) {
        n.isNew = 0
      }
    },
    openMenu() {
      // if (document.querySelector('.nav-list').style.display == 'none')
      //   document.querySelector('.nav-list').style.display = 'initial'
      // else document.querySelector('.nav-list').style.display = 'none'
      this.menuMobileOpen = !this.menuMobileOpen
    },
    OpenNotification() {
      this.newNotifCount = 0
      if (this.notifications.length)
        localStorage.setItem('last-notif-id', this.notifications[0].id)
    },
    MobileBack() {
      this.setPageFocused(false)
      document.querySelector('.page').scrollLeft = 0
    },
    MessageClick(item) {
      this.$router.push({
        path: `/Main/RTLH/InputProposal/`,
        query: {
          NIK: item.NIK,
          Kabupaten: item.Kabupaten,
          Kecamatan: item.Kecamatan,
          Kelurahan: item.Kelurahan,
          Tahun: item.Tahun,
          ProposalID: item.ProposalID,
          tabId: 2,
        },
      })
    },
  },
}
</script>
<style lang="scss">
.v-application {
  &.backlog {
    .nav-bar,
    nav ul li a,
    .ui-table table thead .--header th {
      background: #003300;
    }
    nav ul li a:hover,
    nav ul li a:visited:hover {
      background: #1b5e20;
      color: #ffffff;
    }
  }

  &.rtlh {
    .nav-bar,
    nav ul li a,
    .ui-table table thead .--header th {
      background: #262626;
    }
    nav ul li a:hover,
    nav ul li a:visited:hover {
      background: #2ab1ce;
      color: #ffffff;
    }
  }
}
.nav-bar {
  height: 50px;

  ul {
    padding-left: 0px !important;
    z-index: 10;
  }
  .nav-list {
    li:hover {
      & > ul {
        display: initial;
      }
    }
  }
}

.brand {
  position: absolute;
  padding-left: 20px;
  float: left;
  line-height: 70px;
  text-transform: uppercase;
  font-size: 1.4em;
}
.brand a img {
  max-height: 70px;
}
.brand a,
.brand a:visited {
  color: #ffffff;
  text-decoration: none;
}

.nav-container {
  max-width: calc(100vw - 40px);
  margin: 0 auto;
}

nav {
  /* float: right;*/
  left: 100px;
  position: relative;
  z-index: 2;
}
nav ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
nav ul li {
  float: left;
  position: relative;
}
nav ul li a,
nav ul li a:visited {
  display: block;
  padding: 0 20px;
  line-height: 50px;
  /*background: #262626;*/
  color: #ffffff !important;
  text-decoration: none;
}
nav ul li a:hover,
nav ul li a:visited:hover {
  background: #2ab1ce;
  color: #ffffff;
}
nav ul li a:not(:only-child):after,
nav ul li a:visited:not(:only-child):after {
  padding-left: 4px;
  content: ' ▾';
}
nav ul li ul li {
  min-width: 190px;
}
nav ul li ul li a {
  padding: 15px;
  line-height: 20px;
}

.nav-dropdown {
  position: absolute;
  display: none;
  z-index: 1;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
}
.nav-mobile {
  display: none;
  position: absolute;
  top: 0;
  right: 0;
  background: #262626;
  height: 50px;
  width: 70px;
}
#bell_popover {
  width: 350px;
  margin-left: -350px;
  margin-top: 30px;
  cursor: pointer;
  max-height: 440px;
  overflow-y: auto;

  .v-list-item__title {
    font-size: small;
    margin-bottom: -5px;
    font-weight: 500;
  }
}
#bell_div {
  float: right;
  padding: 12px 0;
}
#name_div {
  display: inline-block;
  position: absolute;
  right: 60px;
  top: 13px;
  color: gray;
}

.is-mobile {
  #bell_popover {
    width: 100vw;
    position: fixed;
    top: 20px;
    right: 0;
  }
}

@media only screen and (max-width: 798px) {
  .nav-mobile {
    display: block;
  }

  #bell_div {
    top: 1px;
    position: absolute;
    right: 75px;
    z-index: 100;
  }
  #name_div {
    top: 15px;
    right: 110px;
  }
  nav {
    width: 100%;
    // padding: 70px 0 15px;
    left: 0;
  }
  // nav ul {
  //   display: none;
  // }
  nav ul li {
    float: none;
  }
  nav ul li a {
    padding: 15px;
    line-height: 20px;
    padding-left: 25%;
  }
  nav ul li ul li a {
    padding-left: 30%;
  }

  .nav-dropdown {
    position: static;
  }
  .brand a img {
    max-height: 40px;
    margin-top: 7px;
    margin-left: -20px;
  }
  .nav-list {
    position: relative;
    // top: -20px;
    top: 50px;
  }
}
@media screen and (min-width: 799px) {
  .nav-list {
    display: block !important;
    position: relative;
  }
}
#nav-toggle {
  position: absolute;
  left: 24px;
  top: -2px;
  cursor: pointer;
  padding: 10px 35px 16px 0px;
}
#nav-toggle span,
#nav-toggle span:before,
#nav-toggle span:after {
  cursor: pointer;
  border-radius: 1px;
  height: 5px;
  width: 35px;
  background: #ffffff;
  position: absolute;
  display: block;
  content: '';
  transition: all 300ms ease-in-out;
}
#nav-toggle span:before {
  top: -10px;
}
#nav-toggle span:after {
  bottom: -10px;
}
#nav-toggle.active span {
  background-color: transparent;
}
#nav-toggle.active span:before,
#nav-toggle.active span:after {
  top: 0;
}
#nav-toggle.active span:before {
  transform: rotate(45deg);
}
#nav-toggle.active span:after {
  transform: rotate(-45deg);
}
</style>
