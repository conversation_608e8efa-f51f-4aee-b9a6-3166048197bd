<template>
  <div class="rekap-angka">
    <div class="baris">
      <div class="kotak">
        <div>Total RTLH</div>
        <div>{{ $filters.format(rekap.TotalRTLH) }}</div>
      </div>
      <div class="kotak">
        <div>
          Intervensi
          <v-icon
            small
            style="margin-top:-3px"
            v-tooltip="'Sudah mendapatkan bantuan'"
          >
            mdi-help-circle-outline
          </v-icon>
        </div>
        <div>{{ $filters.format(rekap.TotalRTLHLayakHuni) }}</div>
      </div>
      <div class="kotak">
        <div>
          Validasi
          <v-icon
            small
            style="margin-top:-3px"
            v-tooltip="'Double Data / Meninggal'"
          >
            mdi-help-circle-outline
          </v-icon>
        </div>
        <div>{{ $filters.format(rekap.TotalRTLHValidasi) }}</div>
      </div>
      <div class="kotak">
        <div>Sisa</div>
        <div>{{ $filters.format(rekap.TotalRTLHSisa) }}</div>
        <div class="sub-kotak" style="padding-left:10px">
          <div>BDT</div>
          <div>
            {{ $filters.format(rekap.TotalRTLHSisaBDT) }}
          </div>
          <div style="padding-left:10px">Lengkap</div>
          <div style="padding-left:10px">
            {{ $filters.format(rekap.TotalRTLHSisaBDT1) }}
          </div>
          <div style="padding-left:10px">Blm Lengkap</div>
          <div style="padding-left:10px">
            {{ $filters.format(rekap.TotalRTLHSisaBDT0) }}
          </div>
        </div>
        <div class="sub-kotak" style="padding-left:10px">
          <div>Non-BDT</div>
          <div>
            {{ $filters.format(rekap.TotalRTLHSisaNonBDT) }}
          </div>
          <div style="padding-left:10px">Lengkap</div>
          <div style="padding-left:10px">
            {{ $filters.format(rekap.TotalRTLHSisaNonBDT1) }}
          </div>
          <div style="padding-left:10px">Blm Lengkap</div>
          <div style="padding-left:10px">
            {{ $filters.format(rekap.TotalRTLHSisaNonBDT0) }}
          </div>
        </div>
      </div>
      <div class="kotak">
        <div>BDT</div>
        <div>{{ $filters.format(rekap.TotalRTLHBDT) }}</div>
        <div style="padding-left:10px">Syarat Lengkap</div>
        <div style="padding-left:10px">{{ $filters.format(rekap.TotalRTLHBDT1) }}</div>
        <div style="padding-left:10px">Belum Lengkap</div>
        <div style="padding-left:10px">{{ $filters.format(rekap.TotalRTLHBDT0) }}</div>
      </div>
      <div class="kotak">
        <div>Non-BDT</div>
        <div>{{ $filters.format(rekap.TotalRTLHNonBDT) }}</div>
        <div style="padding-left:10px">Syarat Lengkap</div>
        <div style="padding-left:10px">
          {{ $filters.format(rekap.TotalRTLHNonBDT1) }}
        </div>
        <div style="padding-left:10px">Belum Lengkap</div>
        <div style="padding-left:10px">
          {{ $filters.format(rekap.TotalRTLHNonBDT0) }}
        </div>
      </div>
    </div>
    <div class="baris">
      <div class="kotak">
        <div>Total Backlog</div>
        <div>{{ $filters.format(rekap.TotalBacklog) }}</div>
      </div>
      <div class="kotak">
        <div>
          Intervensi
          <v-icon
            small
            style="margin-top:-3px"
            v-tooltip="'Sudah mendapatkan bantuan'"
          >
            mdi-help-circle-outline
          </v-icon>
        </div>
        <div>{{ $filters.format(rekap.TotalBacklogLayak) }}</div>
      </div>
      <div class="kotak">
        <div>
          Validasi
          <v-icon
            small
            style="margin-top:-3px"
            v-tooltip="'Double Data / Meninggal'"
          >
            mdi-help-circle-outline
          </v-icon>
        </div>
        <div>{{ $filters.format(rekap.TotalBacklogValidasi) }}</div>
      </div>
      <div class="kotak">
        <div>Sisa</div>
        <div>{{ $filters.format(rekap.TotalBacklogSisa) }}</div>
        <div class="sub-kotak">
          <div style="padding-left:10px">BDT</div>
          <div style="padding-left:10px">
            {{ $filters.format(rekap.TotalBacklogSisaBDT) }}
          </div>
          <div style="padding-left:20px">Lengkap</div>
          <div style="padding-left:20px">
            {{ $filters.format(rekap.TotalBacklogSisaBDT1) }}
          </div>
          <div style="padding-left:20px">Blm Lengkap</div>
          <div style="padding-left:20px">
            {{ $filters.format(rekap.TotalBacklogSisaBDT0) }}
          </div>
        </div>
        <div class="sub-kotak">
          <div style="padding-left:10px">Non-BDT</div>
          <div style="padding-left:10px">
            {{ $filters.format(rekap.TotalBacklogSisaNonBDT) }}
          </div>
          <div style="padding-left:20px">Lengkap</div>
          <div style="padding-left:20px">
            {{ $filters.format(rekap.TotalBacklogSisaNonBDT1) }}
          </div>
          <div style="padding-left:20px">Blm Lengkap</div>
          <div style="padding-left:20px">
            {{ $filters.format(rekap.TotalBacklogSisaNonBDT0) }}
          </div>
        </div>
      </div>
      <div class="kotak">
        <div>BDT</div>
        <div>
          {{ (rekap.TotalBacklogBDT1 + rekap.TotalBacklogBDT0) | format }}
        </div>
        <div style="padding-left:10px">Syarat Lengkap</div>
        <div style="padding-left:10px">
          {{ $filters.format(rekap.TotalBacklogBDT1) }}
        </div>
        <div style="padding-left:10px">Belum Lengkap</div>
        <div style="padding-left:10px">
          {{ $filters.format(rekap.TotalBacklogBDT0) }}
        </div>
      </div>
      <div class="kotak">
        <div>Non-BDT</div>
        <div>
          {{ (rekap.TotalBacklogNonBDT0 + rekap.TotalBacklogNonBDT1) | format }}
        </div>
        <div style="padding-left:10px">Syarat Lengkap</div>
        <div style="padding-left:10px">
          {{ $filters.format(rekap.TotalBacklogNonBDT1) }}
        </div>
        <div style="padding-left:10px">Belum Lengkap</div>
        <div style="padding-left:10px">
          {{ $filters.format(rekap.TotalBacklogNonBDT0) }}
        </div>
      </div>
    </div>
    <div class="baris">
      <div class="kotak" style="width:calc(33vw - 20px)">
        <div>Data Kemiskinan</div>
        <div>{{ $filters.format(rekap.TotalMiskin) }}</div>
      </div>
      <div class="kotak" style="width:calc(33vw - 20px)">
        <div>
          Validasi
          <v-icon
            small
            style="margin-top:-3px"
            v-tooltip="'Double Data / Meninggal'"
          >
            mdi-help-circle-outline
          </v-icon>
        </div>
        <div>{{ $filters.format(rekap.TotalMiskinValidasi) }}</div>
      </div>
      <div class="kotak" style="width:calc(33vw - 20px)">
        <div>Sisa</div>
        <div>{{ $filters.format(rekap.TotalMiskinSisa) }}</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data: () => ({
    rekap: {},
  }),
  async mounted() {
    let res = await this.$api.call('EVO_RptRekapAngka')
    this.rekap = res.data[0]
  },
}
</script>
<style lang="scss">
.rekap-angka {
  max-width: 300vw;
  .baris {
    display: flex;
    padding: 15px;
    background: rgba(255, 255, 255, 0.5);
    margin-bottom: 10px;
    justify-content: space-between;

    .kotak {
      background: rgba(255, 255, 255, 0.7);
      border-radius: 5px;
      padding: 15px;
      width: calc(17% - 20px);
      box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);

      & > div:nth-child(odd) {
        font-size: 14px;
        font-weight: bold;
      }

      & .sub-kotak {
        display: inline-block;
        vertical-align: top;
        font-size: small !important;
        font-weight: normal !important;
        margin-right: 10px;
        & > div:nth-child(odd) {
          font-size: small;
          font-weight: bold;
        }
      }
    }
  }
}
</style>
