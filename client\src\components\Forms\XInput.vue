<template>
  <div class="form-coms ui-input">
    <div
      v-if="$attrs.label"
      class="form-label"
      :class="{ '--required': hasError }"
    >
      <slot name="label">
        {{ $attrs.label }}
      </slot>
    </div>
    <div
      class="--pc-input"
      :style="{
        width: width,
      }"
    >
      <IconField>
        <InputIcon v-if="leftIcon" class="mdi" :class="leftIcon" />
        <InputText
          v-if="type == 'text'"
          v-model="vmodel"
          size="small"
          type="text"
          class="w-full"
        />
        <Password
          v-else-if="type == 'password'"
          v-model="vmodel"
          size="small"
          toggle-mask
          class="w-full"
        />
        <InputNumber
          v-else-if="type == 'number'"
          v-model="vmodel"
          size="small"
          input-id="integeronly"
          class="w-full"
          fluid
        />
        <InputIcon v-if="rightIcon" class="mdi" :class="rightIcon" />
      </IconField>
      <Message
        v-if="remarks"
        class="--remarks"
        size="small"
        severity="secondary"
        variant="simple"
      >
        {{ remarks }}
      </Message>
      <!-- <div
        class="--input"
        :class="{ '--type-masked': type == 'number', '--focused': isFocused }"
        :style="{
          width: width,
        }"
      >
        <v-icon v-if="leftIcon" left @click="leftIconClick">
          {{ leftIcon }}
        </v-icon>
        <div v-if="prefix" class="prefix">{{ prefix }}</div>
        <input
          v-bind="$attrs"
          v-model="vmodel"
          :type="typeLocal"
          class="--real"
          :min="min"
          :style="{
            'margin-left': leftIcon ? '-36px' : undefined,
            'padding-left': leftIcon ? '30px' : undefined,
            'padding-right': rightIcon ? '30px' : undefined,
          }"
          @click="handleClick($event)"
          @blur="handleBlur($event)"
          @keyup="handleKeyup($event)"
          @keydown="handleKeydown($event)"
          @change="handleChange($event)"
        />
        <input
          v-if="type == 'number'"
          v-bind="$attrs"
          v-model="mask"
          type="text"
          class="--mask"
          :style="{
            'margin-left': leftIcon ? '-36px' : undefined,
            'padding-left': leftIcon ? '30px' : undefined,
            'padding-right': rightIcon ? '30px' : undefined,
            'text-align': type == 'number' ? 'right' : 'left',
          }"
          @mouseenter="handleMouseEnter"
        />
        <div v-if="postfix" class="postfix">{{ postfix }}</div>
        <v-icon v-if="rightIconLocal" small right @click="rightIconClick">
          {{ rightIconLocal }}
        </v-icon>
      </div> -->
    </div>
  </div>
</template>

<script>
import InputText from 'primevue/inputtext'
import Password from 'primevue/password'
import IconField from 'primevue/iconfield'
import InputIcon from 'primevue/inputicon'
import InputNumber from 'primevue/inputnumber'
import Message from 'primevue/message'
export default {
  name: 'XInput',
  components: {
    InputText,
    Password,
    IconField,
    InputIcon,
    InputNumber,
    Message,
  },
  props: {
    value: {
      type: [String, Number],
      default: null,
    },
    width: {
      type: String,
      default: '100%',
    },
    leftIcon: {
      type: String,
      default: null,
    },
    rightIcon: {
      type: String,
      default: null,
    },
    prefix: {
      type: String,
      default: null,
    },
    postfix: {
      type: String,
      default: null,
    },
    format: {
      type: String,
      default: null,
    },
    remarks: {
      type: String,
      default: null,
    },
    allowNegative: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: 'text',
    },
  },
  emits: [
    'update:value',
    'blur',
    'input',
    'change',
    'click',
    'keyup',
    'left-icon-click',
    'right-icon-click',
  ],
  data: () => ({
    val: '',
    timeoutTracker: null,
    mask: '',
    toggleMask: true,
    isFocused: false,
    rightIconLocal: '',
    typeLocal: '',
    regex: null,
  }),
  computed: {
    min() {
      if (this.allowNegative) return undefined
      else return 0
    },
    vmodel: {
      get() {
        return this.val === null || this.val === undefined
          ? this.value
          : this.val
      },
      set(val) {
        if (val && this.regex && !String(val).match(this.regex)) {
          return
        }
        this.val = val
        if (this.rightIcon != 'search') {
          if (this.type == 'number') {
            if (val && this.format != '#000')
              this.mask = parseFloat(val).toLocaleString()
            else this.mask = val
          }
          this.$emit('update:value', val)
        } else {
          clearTimeout(this.timeoutTracker)
          this.timeoutTracker = setTimeout(() => {
            this.$emit('update:value', val)
          }, 800)
        }
      },
    },
    hasError() {
      return (
        this.$attrs.label &&
        this.$attrs.label.match(/\*$/) &&
        (this.vmodel === null ||
          this.vmodel === undefined ||
          this.vmodel === '')
      )
    },
  },
  watch: {
    value(val) {
      this.val = val
      if (this.type == 'number') {
        if (val && this.format != '#000')
          this.mask = parseFloat(val).toLocaleString()
        else this.mask = val
      }
    },
  },
  created() {
    this.val = this.value
    this.typeLocal = this.type
    this.rightIconLocal = this.rightIcon
    if (this.type == 'number') {
      this.regex = /^[\d]+.?\d{0,}/
      if (this.val && this.format != '#000')
        this.mask = parseFloat(this.val).toLocaleString()
      else this.mask = this.val
    } else if (this.type == 'password') {
      this.rightIconLocal = 'mdi-eye-off'
    }
    if (this.$attrs.pattern) {
      this.regex = new RegExp(this.$attrs.pattern)
    }
  },
  methods: {
    handleBlur(evt) {
      this.isFocused = false
      this.$emit('blur', evt)
    },
    handleInput(evt) {
      this.$emit('input', this.vmodel, evt)
    },
    handleChange(evt) {
      this.$emit('change', this.vmodel, evt)
    },
    handleMouseEnter() {
      // this.toggleMask = false
    },
    handleClick(evt) {
      this.isFocused = true
      this.$emit('click', evt)
    },
    handleKeyup(evt) {
      this.$emit('keyup', evt)
    },
    handleKeydown() {
      // if (
      //   this.type === 'number' &&
      //   evt.key.length === 1 &&
      //   !(String(this.val || '').replace(/,/g, '') + evt.key).match(
      //     /^\d{0,}.\d{0,}$/
      //   )
      // )
      //   evt.preventDefault()
    },
    leftIconClick() {
      this.$emit('left-icon-click')
    },
    rightIconClick() {
      if (this.type == 'password') {
        if (this.rightIconLocal.match(/off$/)) {
          this.rightIconLocal = 'mdi-eye'
          this.typeLocal = 'text'
        } else {
          this.rightIconLocal = 'mdi-eye-off'
          this.typeLocal = 'password'
        }
      } else {
        this.$emit('right-icon-click')
      }
    },
  },
}
</script>
<style lang="scss">
.p-inputicon.mdi {
  font-size: 24px !important;
  margin-top: -11px !important;
  margin-left: -4px !important;
}
.p-iconfield {
  input {
    width: 100%;
  }
}
.w-full {
  width: 100% !important;
}
// .ui-input {
//   margin-bottom: 8px;
//   font-size: 14px;

//   .form-label {
//     text-align: left;

//     &.--required {
//       color: red;
//     }
//   }
//   .--pc-input {
//     width: 100%;
//   }
//   .--input {
//     display: flex;
//     width: 180px;
//     max-width: 100%;
//     input {
//       padding: 4px 6px;
//       background: rgba(200, 200, 200, 0.2);
//       border-radius: 2px;
//       border-bottom: 1px solid silver;
//       width: 100%;

//       &[type='number'] {
//         text-align: right;
//       }
//       &:hover,
//       &:focus {
//         border: 0;
//         background: rgba(200, 200, 200, 0.4);
//         border-bottom: 1px solid gray;
//       }
//     }
//     &.--type-masked {
//       .--real {
//         display: none;
//       }
//       .--mask {
//         display: inline-block;
//       }
//       &:hover {
//         .--real {
//           display: inline-block;
//         }
//         .--mask {
//           display: none;
//         }
//       }
//       &.--focused {
//         .--real {
//           display: inline-block !important;
//         }
//         .--mask {
//           display: none !important;
//         }
//       }
//     }
//     .postfix,
//     .prefix {
//       padding: 4px 8px;
//       border-bottom: 1px solid silver;
//       background: #f3f3f3;
//     }
//     .v-icon--right {
//       margin-left: 5px;
//     }
//     .v-icon--right {
//       margin-left: -25px;
//     }
//   }
//   .--remarks {
//     font-size: small;
//     color: gray;
//   }
//   .v-icon.v-icon::after {
//     height: 80%;
//     top: 10%;
//   }
// }
.dense {
  .ui-input {
    font-size: 14px;
  }
}
</style>
