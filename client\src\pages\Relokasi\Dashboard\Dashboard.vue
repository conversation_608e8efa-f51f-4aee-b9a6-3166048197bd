<template>
  <Page title="Dashboard Relokasi">
    <div
      style="position: absolute; width: 100vw; text-align: center; top: 55px"
      v-if="hasRTLH && hasRelokasi"
    >
      <v-btn rounded @click="GoToRTLH" v-if="hasRTLH" style="z-index: 1">
        RTLH
      </v-btn>
      <v-btn
        text
        style="
          font-weight: bold;
          background: #ddd;
          padding-left: 50px;
          margin-left: -40px;
          box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.5);
        "
        rounded
        v-if="hasRelokasi"
        >Relokasi</v-btn
      >
    </div>
    <div style="display: flex; height: 100%">
      <div style="flex: 2; background: white" v-if="this.user.UserID < 5">
        <XSelect
          v-model:value="tipeData"
          :items="[{ val: 'Kepemilikan', txt: 'Kepemilikan' }]"
          style="margin-left: calc(50% - 80px)"
        />
        <RelokasiChart />
      </div>
      <div style="flex: 2; margin: 0 5px" v-if="this.user.UserID < 5"></div>
      <div id="dvLastUpdate" class="boxboard" style="flex: 2; max-width: 34vw">
        <LastActivities />
      </div>
    </div>
  </Page>
</template>
<script>
import { mapGetters } from 'vuex'
import RelokasiChart from './RelokasiChart.vue'
import LastActivities from './LastActivities.vue'
export default {
  components: {
    RelokasiChart,
    LastActivities,
  },
  data: () => ({
    tipeData: 'Kepemilikan',
  }),
  computed: {
    ...mapGetters({
      user: 'getUser',
    }),
    hasRelokasi() {
      let menu = JSON.parse(localStorage.getItem('menu'))
      return menu.find((m) => {
        return m.MenuName === 'Relokasi'
      })
    },
    hasRTLH() {
      let menu = JSON.parse(localStorage.getItem('menu'))
      return (menu || []).find((m) => {
        return m.MenuName === 'RTLH'
      })
    },
  },
  methods: {
    GoToRTLH() {
      sessionStorage.setItem('dashboard', 'rtlh')
      this.$router.push('/Main/App/Home')
    },
  },
}
</script>
