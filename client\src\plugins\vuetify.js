// import Vue from 'vue' // Not needed in Vue 3
import { createVuetify } from 'vuetify' // Import createVuetify
import 'vuetify/styles' // Import Vuetify styles
import * as components from 'vuetify/components' // Import components
import * as directives from 'vuetify/directives' // Import directives

// Vue.use(Vuetify) // Not needed in Vue 3

export default createVuetify({
  components,
  directives,
  // Add any Vuetify 3 options here
})
