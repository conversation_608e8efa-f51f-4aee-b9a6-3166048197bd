<template>
  <Page :title="isMobile ? '-' : 'Dashboard'">
    <div
      style="position: absolute; width: 100vw; text-align: center; top: 55px"
      v-if="hasRTLH && hasBacklog"
    >
      <v-btn
        text
        style="
          font-weight: bold;
          background: #ddd;
          padding-right: 50px;
          box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.5);
        "
        rounded
        v-if="hasRTLH"
      >
        RTLH
      </v-btn>
      <v-btn
        style="margin-left: -40px"
        rounded
        @click="GoToBacklog"
        v-if="hasBacklog"
      >
        Backlog
      </v-btn>
    </div>
    <div
      v-if="[1, 8].includes(user.RolePositionID)"
      style="height: 100%; overflow: auto; max-width: 300vw"
    >
      <Speedometer />
      <div
        style="
          display: flex;
          height: 100vh;
          overflow-y: hidden;
          max-width: 300vw;
        "
        v-if="[1, 2, 8].includes(user.RolePositionID)"
      >
        <div style="flex: 2; background: white; max-width: 100vw">
          <XSelect
            v-model:value="tipeData"
            :items="[
              { val: '115', txt: 'DATA 2015' },
              { val: '118', txt: 'DATA 2018' },
              { val: '119', txt: 'DATA 2019' },
              { val: '120', txt: 'DATA 2020' },
              { val: '121', txt: 'DATA 2021' },
              { val: '122', txt: 'DATA 2022' },
              { val: '123', txt: 'DATA 2023' },
              { val: '124', txt: 'DATA 2024' },
            ]"
            style="margin-left: calc(50% - 80px)"
          />
          <YearlyChart :tipeData="tipeData" />
        </div>
      </div>
      <Quarterly :tipeData="tipeData" />
    </div>
    <div v-else style="height: 100%; overflow: auto; max-width: 300vw">
      <div
        class="infografis-container"
        v-if="[1, 2, 8].includes(user.RolePositionID)"
      >
        <Infografis />
      </div>
      <div
        style="
          display: flex;
          height: 100vh;
          overflow-y: hidden;
          max-width: 300vw;
        "
        v-if="[1, 2, 8].includes(user.RolePositionID)"
      >
        <div style="flex: 2; background: white; max-width: 100vw">
          <XSelect
            v-model:value="tipeData"
            :items="[
              { val: '115', txt: 'DATA 2015' },
              { val: '118', txt: 'DATA 2018' },
              { val: '119', txt: 'DATA 2019' },
              { val: '120', txt: 'DATA 2020' },
              { val: '121', txt: 'DATA 2021' },
              { val: '122', txt: 'DATA 2022' },
              { val: '123', txt: 'DATA 2023' },
              { val: '124', txt: 'DATA 2024' },
            ]"
            style="margin-left: calc(50% - 80px)"
          />
          <YearlyChart :tipeData="tipeData" />
        </div>
        <div style="flex: 2; margin: 0 5px; max-width: 100vw">
          <!-- <Rekap /> -->
          <NeedAttention />
        </div>
        <div
          id="dvLastUpdate"
          class="boxboard"
          style="flex: 2; max-width: 100vw"
        >
          <LastActivities />
        </div>
      </div>
      <RekapAngka v-if="[1, 2, 8].includes(user.RolePositionID)" />
    </div>
  </Page>
</template>

<script>
// @ is an alias to /src
import Infografis from './Infografis.vue'
import NeedAttention from './NeedAttention.vue'
import LastActivities from './LastActivities.vue'
// import Rekap from './Rekap.vue'
import YearlyChart from './YearlyChart.vue'
import RekapAngka from './RekapAngka.vue'
import { mapGetters } from 'vuex'
import Speedometer from './Speedometer.vue'
import Quarterly from './Quarterly.vue'

export default {
  name: 'Home',
  components: {
    NeedAttention,
    LastActivities,
    // Rekap,
    YearlyChart,
    RekapAngka,
    Infografis,
    Speedometer,
    Quarterly,
  },
  data: () => ({
    tipeData: '115',
    chartOptions: {
      title: {
        display: false,
        text: 'Chart',
      },
      legend: {
        display: false,
        labels: {
          usePointStyle: true,
          pointRadius: 2,
        },
      },
      responsive: true,
      maintainAspectRatio: false,
    },
  }),
  computed: {
    ...mapGetters({
      user: 'getUser',
    }),
    hasBacklog() {
      let menu = JSON.parse(localStorage.getItem('menu'))
      return menu.find((m) => {
        return m.MenuName === 'Backlog'
      })
    },
    hasRTLH() {
      let menu = JSON.parse(localStorage.getItem('menu'))
      return (menu || []).find((m) => {
        return m.MenuName === 'RTLH'
      })
    },
  },
  created() {
    if (!this.user || !localStorage.getItem('menu')) {
      this.$router.push('/login')
    }
  },
  mounted() {
    if (!this.hasRTLH && this.hasBacklog)
      this.$router.push('/Main/Backlog/Dashboard')
    else if (sessionStorage.getItem('dashboard') == 'backlog')
      this.$router.push('/Main/Backlog/Dashboard')
  },
  methods: {
    GoToBacklog() {
      sessionStorage.setItem('dashboard', 'backlog')
      this.$router.push('/Main/Backlog/Dashboard')
    },
  },
}
</script>
<style lang="scss">
.hide {
  display: none;
}
.infografis-container {
  display: flex;
  align-items: center;
  max-width: 100vw;
  max-height: 70vh;
  overflow: hidden;
  justify-content: center;
  #infografis {
    position: relative;
    max-width: 100vw;
    max-height: 100%;
  }
}
.is-mobile {
  .infografis-container {
    max-height: 40vh;
  }
}
</style>
