const initialState = () => ({
  notification: {
    message: '',
    type: 'success',
  },
  menu: [],
  user: null,
  isFocused: false,
})
const state = initialState()

const getters = {
  getNotification: (state) => state.notification,
  getMenu: (state) => {
    if (state.menu && state.menu.length) return state.menu
    else return JSON.parse(localStorage.getItem('menu'))
  },
  getUser: (state) => {
    if (!state.user) return JSON.parse(localStorage.getItem('user'))
    else return state.user
  },
  isPageFocused: (state) => {
    return state.isFocused
  },
}

const actions = {
  setNotification({ commit }, notification) {
    if (typeof notification === 'string')
      commit(types.SET_NOTIFICATION, {
        message: notification,
        type: 'success',
      })
    else commit(types.SET_NOTIFICATION, notification)
  },
  setMenu({ commit }, data) {
    let menu = []
    if (!data) {
      commit(types.SET_MENU, [])
    } else if (data.data) {
      let last = []
      for (let i = 0; i < data.data.length; i++) {
        let m = data.data[i]
        m.child = []
        if (last.length) {
          while (
            last.length &&
            m.ParentMenuID != last[last.length - 1].MenuID
          ) {
            last.pop()
          }
        }
        if (last.length && m.ParentMenuID == last[last.length - 1].MenuID) {
          last[last.length - 1].child.push(m)
        } else {
          menu.push(m)
        }
        last.push(m)
      }
      commit(types.SET_MENU, menu)
    } else if (data.length && data[0].MenuName) {
      commit(types.SET_MENU, data)
    } else {
      commit(types.SET_MENU, [])
    }
  },
  setUser({ commit }, user) {
    localStorage.setItem('user', JSON.stringify(user))
    commit(types.SET_USER, user)
  },
  setPageFocused({ commit }, isFocused) {
    commit(types.SET_PAGE_FOCUSED, isFocused)
  },
}

const types = {
  SET_NOTIFICATION: 'common/SET_NOTIFICATION',
  SET_MENU: 'common/SET_MENU',
  SET_USER: 'common/SET_USER',
  SET_PAGE_FOCUSED: 'common/SET_PAGE_FOCUSED',
}

const mutations = {
  [types.SET_NOTIFICATION](state, notification) {
    state.notification = notification
  },
  [types.SET_MENU](state, menu) {
    localStorage.setItem('menu', JSON.stringify(menu))
    state.menu = menu
  },
  [types.SET_USER](state, user) {
    localStorage.setItem('user', JSON.stringify(user))
    state.user = user
  },
  [types.SET_PAGE_FOCUSED](state, isFocused) {
    state.isFocused = isFocused
  },
}

export default {
  state,
  mutations,
  getters,
  actions,
  types,
}
