<template>
  <Page title="Monev & LPJ" :sidebar="true">
    <template v-slot:toolbar>
      <v-icon @click="doPrint++" v-tooltip="'Download Excel'"
        >mdi-microsoft-excel</v-icon
      >
    </template>
    <Sidebar
      v-model:value="area"
      :rebind="rebindSidebar"
      dbref="PRM.SelMonevArea"
      :tahun="new Date().getFullYear()"
      :tabs="[2, 4, 13]"
    />
    <div style="padding: 0 10px; width: 100%" v-show="area.Kelurahan">
      <DesaGlance
        :dbparams="area"
        :jmlUsulan="jmlUsulan"
        @clickTambahBaru="ClickTambahBaru"
      />
      <Grid
        v-model:datagrid="datagrid"
        :dbref="dbref"
        :dbparams="area"
        :requires="['Kabupaten']"
        :filter="filterGrid"
        :autopaging="false"
        :disabled="true"
        :doPrint="doPrint"
        :doRebind="doRebind"
        groupBy="Tahapan"
        height="calc(100vh - 243px)"
        :columns="[
          {
            name: 'RK',
            value: 'Tahapan',
            class: 'center',
          },
          {
            name: 'NIK',
            value: 'NIK',
          },
          {
            name: 'Nama',
            value: 'KRT_Nama',
            width: '200px',
            class: 'plain fix-width',
          },
          {
            name: 'Alamat',
            value: 'Alamat',
            width: '250px',
            class: 'fix-width',
          },
          {
            name: 'Skor',
            value: 'ScoreTag',
          },
          {
            name: 'Prior',
            value: 'Prioritas',
            class: 'center',
          },
          {
            name: 'DT',
            value: 'TipeData',
          },
          {
            name: '',
            value: 'Dokumen',
            class: 'plain center',
          },
          {
            name: '',
            value: 'Checklist',
            class: 'plain center',
          },
        ]"
      >
        <template v-slot:group-row="{ row, columns }">
          <td
            class="group-row"
            :colspan="columns.length"
            style="background: #eee"
            v-show="row.IsApproved"
          >
            <div style="display: flex">
              <div style="font-weight: bold; font-size: 14px">
                {{ row.Tahapan || 'Belum Direkom' }}
              </div>
              <v-spacer />
              <v-btn
                x-small
                :outlined="!row.IsComplete"
                :color="row.IsComplete ? 'success' : 'primary'"
                @click="OpenPadatKarya(row.Tahapan, row.KodeWilayah)"
              >
                upload pertanggungjawaban desa
              </v-btn>
              <v-btn
                x-small
                text
                color="primary"
                v-show="false"
                @click="$router.push('/Main/RTLH/RekomProposal/')"
              >
                halaman rekom
              </v-btn>
            </div>
          </td>
        </template>
        <template #row-NIK="{ row }">
          <nik-block :nik="row.NIK" />
        </template>
        <template #row-Tahapan="{ row }">
          <rekom-block :rekom="row?.Tahapan?.replace('REKOM ', '')" />
        </template>
        <template #row-KRT_Nama="{ row }">
          <v-btn
            text
            small
            color="primary"
            @click="OpenDetail(row.NoRef)"
            style="width: 165px; justify-content: left"
          >
            {{ row.KRT_Nama }}
          </v-btn>
        </template>
        <template #row-Prioritas="{ row }">
          <v-icon
            :color="priorcolors[row.Prioritas]"
            v-tooltip="`Prioritas ${row.Prioritas}`"
          >
            mdi-numeric-{{ row.Prioritas }}-circle
          </v-icon>
        </template>
        <template #row-TipeData="{ row }">
          {{ parseInt((row.TipeData + '').substr(-2)) + 2000 }}
        </template>
        <template #row-Checklist="{ row }">
          <v-icon
            :color="row.IsMonevComplete ? 'primary' : ''"
            @click="OpenChecklist(row.NIK)"
            v-tooltip="'Berkas Monitoring'"
            style="margin-right: 15px"
          >
            mdi-monitor-eye
          </v-icon>
          <v-icon
            :color="row.IsLPJComplete ? 'primary' : ''"
            @click="OpenLPJ(row.NIK, row)"
            v-tooltip="'Laporan Pertanggungjawaban'"
            style="margin-right: 15px"
          >
            mdi-file-sign
          </v-icon>
          <v-icon
            @click="OpenChecklistLPJ2(row.NIK)"
            :color="row.IsCheckComplete ? 'success' : ''"
          >
            mdi-clipboard-list
          </v-icon>
        </template>
      </Grid>
      <ValidasiDetail
        v-model:show="showDetailModal"
        :noRef="selectedRef"
        :area="area"
      />
      <ChecklistByRekom v-model:area="area" :show="showChecklistByRekom" />
      <ChecklistMonev v-model:nik="selectedNIK" :show="showChecklistModal" />
      <ChecklistLPJ
        :nik="selectedNIK"
        v-model:show="showChecklistLPJ"
        :rowData="selectedRow"
      />
      <ChecklistLPJ2 v-model:nik="selectedNIK" :show="showChecklistLPJ2" />
    </div>
  </Page>
</template>
<script>
import Sidebar from './SideBar.vue'
import DesaGlance from '../DesaGlance.vue'
import ValidasiDetail from '../InputUsulan/ValidasiDetail.vue'
import ChecklistMonev from './Checklist.vue'
import ChecklistLPJ from './ChecklistLPJ.vue'
import ChecklistLPJ2 from './ChecklistLPJ2.vue'
import ChecklistByRekom from './ChecklistByRekom.vue'
import NikBlock from '../../../components/NikBlock.vue'

export default {
  components: {
    Sidebar,
    DesaGlance,
    ValidasiDetail,
    ChecklistByRekom,
    ChecklistMonev,
    ChecklistLPJ,
    ChecklistLPJ2,
    NikBlock,
  },
  data: () => ({
    priorcolors: ['#333', '#e53935', '#F4511E', '#43A047', '#1E88E5'],
    datagrid: [],
    area: {},
    showChecklistByRekom: false,
    showDetailModal: false,
    showProposalModal: false,
    showChecklistModal: false,
    showChecklistLPJ: false,
    showChecklistLPJ2: false,
    showGantiPenerima: false,
    selectedRef: null,
    selectedNIK: null,
    selectedRow: null,
    doPrint: 0,
    doRebind: 0,
    rebindSidebar: 0,

    IsRekom: false,
    NoRefs: ',',
  }),
  computed: {
    dbref() {
      if (this.area.tabId == 2 || !this.area.tabId) {
        return 'PRM.MonevDet'
      } else {
        return 'PRM.ProposalBSPS'
      }
    },
    jmlUsulan() {
      return this.datagrid.filter((d) => {
        return d.CheckedValue && (d.FinalStatus || 'OK') == 'OK'
      }).length
    },
    buttonStatus() {
      return this.datagrid.reduce((total, curr) => {
        if (curr.IsSelected && curr.Tahapan) total['IsCancel'] = true
        if (curr.IsSelected && !curr.Tahapan) total['IsRekom'] = true
        return total
      }, {})
    },
  },
  watch: {
    showDetailModal(val) {
      if (!val) this.selectedRef = null
    },
    datagrid(val) {
      this.IsRekom = false
      this.NoRefs = ','
      if (val && val.length) {
        val.forEach((v) => {
          if (v.IsApproved == 2) this.IsRekom = true
          if (v.IsApproved) this.NoRefs += v.NoRef + ','
        })
      }
    },
  },
  mounted() {
    if (this.$route.query) this.area = this.$route.query
  },
  methods: {
    ClickTambahBaru() {
      this.showDetailModal = true
    },
    OpenDetail(noRef) {
      this.selectedRef = noRef
      this.showDetailModal = true
    },
    OpenPadatKarya(tahapan) {
      this.area.Tahapan = tahapan
      this.showChecklistByRekom = true
    },
    OpenChecklist(nik) {
      this.selectedNIK = nik
      this.showChecklistModal = true
    },
    OpenChecklistLPJ2(nik) {
      this.selectedNIK = nik
      this.showChecklistLPJ2 = true
    },
    OpenLPJ(nik, row) {
      this.selectedNIK = nik
      this.selectedRow = row
      this.showChecklistLPJ = true
    },
    OpenGantiPenerima(nik) {
      this.selectedNIK = nik
      this.showGantiPenerima = true
    },
    filterGrid(row) {
      return Boolean(row.IsApproved)
    },
  },
}
</script>
<style lang="scss">
.page-input-usulan {
  .ui-checkbox {
    .--box.checked {
      color: #1976d2;
    }
  }
}
</style>
