<template>
  <div style="min-height:200px;">
    <XSelect
      label="Kabupaten"
      dbref="Arch.SelArea"
      v-model:value="forms.KabupatenID"
      v-model:text="forms.Kabupaten"
      :dbparams="{ ParentAreaID: 33 }"
    />
    <XSelect
      label="Kecamatan"
      dbref="Arch.SelArea"
      v-model:value="forms.KecamatanID"
      v-model:text="forms.Kecamatan"
      :dbparams="kecamatanParams"
    />
    <XSelect
      label="Kelurahan"
      dbref="Arch.SelArea"
      v-model:text="forms.Kecamatan"
      :dbparams="{ ParentAreaID: forms.KecamatanID }"
    />
  </div>
</template>
<script>
export default {
  data: () => ({
    forms: {},
  }),
  props: {
    value: Object,
  },
  computed: {
    kecamatanParams() {
      return { ParentAreaID: this.forms.KabupatenID }
    },
  },
}
</script>
