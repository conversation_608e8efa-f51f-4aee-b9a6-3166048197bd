<template>
  <Modal title="BERKAS" v-model:show="xshow" width="900px" @onSubmit="Save">
    <div style="display: flex; font-size: 14px">
      <div style="width: 100%">
        <div style="display: flex">
          <Uploader
            label="BERKAS LENGKAP"
            v-model:value="forms.BerkasLengkap"
            accept=".pdf"
          ></Uploader>
          <Uploader
            label="KTP KADES + BEND."
            v-model:value="forms.KtpKades"
            accept=".pdf"
          ></Uploader>
        </div>
        <div style="display: flex">
          <Uploader
            label="KWITANSI"
            v-model:value="forms.Kwitansi"
            accept=".pdf"
          ></Uploader>
          <Uploader
            label="BUKU REKENING"
            v-model:value="forms.BukuRekening"
            accept=".pdf"
          ></Uploader>
        </div>
      </div>
    </div>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    xshow: false,
    forms: {},
  }),
  props: {
    show: Boolean,
    nik: [String, Number],
    param: Object,
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      if (!val) this.forms = {}
      else if (this.xshow) this.populate()
      this.$emit('update:show', val)
    },
  },
  methods: {
    async populate() {
      this.loading = true
      if (this.param) {
        let { data } = await this.$api.call('PRM.SelBerkasFiles', {
          nik: this.nik,
          ...this.param,
        })
        this.forms = data.length ? data[0] : {}
      } else {
        this.forms = {}
      }
      this.loading = false
    },
    async Save() {
      this.error = ''
      let ret = await this.$api.call('PRM.SavBerkasDet', {
        ...this.param,
        ...this.forms,
      })
      if (ret.success) {
        this.xshow = false
        this.$emit('save')
      } else this.error = ret.message
    },
  },
}
</script>
<style lang="scss">
.modal-proposal {
  .v-dialog--content {
    padding: 0;
  }
}
.is-mobile {
  .modal-berkas {
    .ui-upload {
      width: 50%;
      .imgbox {
        width: 100%;
      }
    }
  }
}
</style>
