<template>
  <div
    style="
      background: rgba(255, 255, 255, 0.5);
      display: flex;
      padding: 10px;
      align-items: center;
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    "
  >
    <div style="display: flex; align-items: center">
      <div>
        <img src="/imgs/logo.png" style="max-height: 70px" />
        <div
          style="
            display: inline-block;
            margin-left: 10px;
            position: relative;
            top: -5px;
          "
        >
          <div style="font-size: larger">SIMPERUM</div>
          <div style="font-size: small; color: gray">
            SISTEM INFORMASI PERUMAHAN
          </div>
          <div style="font-size: small; color: gray; margin-top: -3px">
            DISPERAKIM JAWA TENGAH
          </div>
        </div>
      </div>
      <div style="margin-left: 20px">
        <v-btn variant="text" @click="$router.push({ name: 'PetaRTLH' })">
          <v-icon left>mdi-map</v-icon>
          PETA RTLH
        </v-btn>
      </div>
      <div style="margin-left: 20px">
        <v-btn variant="text" @click="$router.push({ name: 'Public' })">
          <v-icon left>mdi-information</v-icon>
          INFORMASI SEPUTAR RTLH
        </v-btn>
      </div>
    </div>
    <v-spacer />
    <!-- <XInput
      type="text"
      class="nik-search"
      style="margin-top: 10px;margin-right: 20px;margin-left: -30px;"
      right-icon="search"
      placeholder="Cari NIK"
    /> -->
    <div class="text-panel">
      <input
        v-model="searchNik"
        type="text"
        style="
          width: calc(100% - 30px);
          font-size: 14px;
          outline-color: white;
          padding: 1px;
        "
        placeholder="Cari NIK ..."
      />
      <v-icon>mdi-magnify</v-icon>
    </div>
    <div style="padding-right: 20px">
      <v-btn color="success" @click="$router.push({ name: 'Login' })">
        <v-icon left>mdi-login</v-icon>
        MASUK
      </v-btn>
    </div>
    <div v-show="showDetail" class="detail-panel">
      <div v-if="detail.KRT_Nama" class="padding">
        <div>
          {{ detail.KRT_Nama }}
        </div>
        <div style="font-size: 12px">
          {{ detail.NIK }}
        </div>
        <div style="font-size: 12px; color: gray">
          {{ detail.Alamat }}
        </div>
        <div style="font-size: 12px; color: gray">
          {{ detail.Kabupaten }}, {{ detail.Kecamatan }},
          {{ detail.Kelurahan }}
        </div>
        <div style="font-size: 12px">
          <b>Bantuan:</b> {{ detail.SumberName }}
        </div>
      </div>
      <div
        v-else-if="searchNik.length == 16"
        class="padding"
        style="font-size: small"
      >
        NIK Tidak Ditemukan
      </div>
      <div v-if="detail.Total" class="padding">
        <div>
          {{ detail.Kabupaten }}
        </div>
        <div style="font-size: 12px">
          {{ detail.Kecamatan }}{{ detail.Kelurahan ? ',' : '' }}
          {{ detail.Kelurahan }}
        </div>
        <div style="font-size: 12px">
          <b>Jml RTLH:</b> {{ $filters.format(detail.Total) }}
          <br />
          <b>Jml Intervensi:</b> {{ $filters.format(detail.Intervensi) }}
          <br />
          <b>Sisa RTLH:</b> {{ $filters.format(detail.Sisa) }}
        </div>
      </div>
      <div
        style="
          display: flex;
          width: 310px;
          max-height: 300px;
          flex-wrap: wrap;
          overflow: auto;
        "
      >
        <div
          v-for="(item, idx) in photos"
          v-show="detail[item.key]"
          :key="idx"
          style="width: 150px"
        >
          <div style="padding: 5px 8px; font-size: 12px; background: #f3f3f3">
            {{ item.text }}
          </div>
          <div
            class="imgbox"
            :style="{
              'background-image': `url(${detail[item.key]})`,
            }"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data: () => ({
    searchNik: '',
    detail: {},
    showDetail: false,
    photos: [],
  }),
  watch: {
    searchNik(val) {
      if (val.length == 16) this.SearchByNIK(val)
    },
  },
  methods: {
    async SearchByNIK(nik) {
      let ret = await this.$api.call('EVO.SelMapPerson', { NIK: nik })
      if (ret.data.length > 0) this.detail = ret.data[0]
      else this.detail = {}
      this.showDetail = true
    },
  },
}
</script>
<style lang="scss" scoped>
.nik-search {
  input {
    padding: 10px;
  }
}
.text-panel {
  background: white;
  margin-top: 2px;
  padding: 5px 8px;
  margin-right: 10px;
  width: 250px;
  border-radius: 5px;
  overflow: hidden;
  max-height: 600px;
}
.detail-panel {
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
  background: white;
  margin-top: 2px;
  padding: 5px 8px;
  margin-right: 10px;
  width: 250px;
  border-radius: 5px;
  overflow: hidden;
  max-height: 600px;
  position: absolute;
  right: 143px;
  top: 70px;
  z-index: 1;
  width: 300px;
}
</style>
