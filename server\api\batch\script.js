var mysql = require("mysql");
var util = require("util");
const fs = require("fs");
//const readline = require("readline");
var readlines = require("n-readlines");
const stream = require("stream");

var pool = mysql.createPool({
  connectionLimit: 10,
  host: "*************",
  user: "devusr",
  port: '3307',
  password: "Simperum2019!",
  database: "perum"
});

// pool.getConnection((err, connection) => {
//   if (err) {
//     if (err.code === "PROTOCOL_CONNECTION_LOST") {
//       console.error("Database connection was closed.");
//     }
//     if (err.code === "ER_CON_COUNT_ERROR") {
//       console.error("Database has too many connections.");
//     }
//     if (err.code === "ECONNREFUSED") {
//       console.error("Database connection was refused.");
//     }
//   }
//   if (connection) connection.release();
//   return;
// });
pool.query = util.promisify(pool.query); // Magic happens here.


// generate delay function
async function sleep(ms) {
  return new Promise((resolve) => {
    setTimeout(resolve, ms);
  });
}

//3308
async function main() {
  var filename = `C:\\Users\\<USER>\\Downloads\\backlog.csv`;

  var liner = new readlines(filename);
  let next = ''
  let start = false
  let i = 1
  while ((next = liner.next())) {
    let line = next.toString("ascii").split(",");
    // if (line[6] == '3302130702880001') start = true
    if (parseInt(line[10]?.trim()) == 2023) start = true
    if (line[6]?.length == 16 && start) {
      console.log(i, line[10].trim(), line[6])
      await sleep(500)
      // let x = await pool.query(`SELECT ProposalDetID, NIK, KRT_Nama FROM blg_proposaldet WHERE NIK = ${line[6]}`)
      // console.log(x)
      // await pool.query(`CALL BLG_SavProposalDetByNIK(?, ?, ?, ?, ?)`, [parseInt(line[10].trim()) - 2013, line[6], 1,2,1]).catch(function(err) {
      await pool.query(`CALL BLG_SavProposalApproval(?, ?, ?)`, [line[6], 1, 1]).catch(function(err) {
        console.error(err.sqlMessage);
        console.error(err.sql);
      // process.exit(0);
      });
    }
    i++
  }
  process.exit(0);
}

main();
