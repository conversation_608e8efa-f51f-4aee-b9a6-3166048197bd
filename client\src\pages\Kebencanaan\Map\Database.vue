<template>
  <Page title="Database Kebencanaan">
    <template v-slot:toolbar>
      <v-btn @click="showDetailModal = true">
        <v-icon left>mdi-plus</v-icon>
        tambah data
      </v-btn>
      <v-icon @click="doPrint++">print</v-icon>
    </template>
    <Grid
      v-if="false"
      v-model:datagrid="backlog"
      dbref="BCN.Kebencanaan"
      :dbparams="filters"
      :disabled="true"
      :height="'calc(100vh - 120px)'"
      :columns="[
        {
          name: '<PERSON><PERSON>',
          value: '<PERSON><PERSON>',
        },
        {
          name: '<PERSON><PERSON><PERSON>',
          value: '<PERSON><PERSON><PERSON>',
        },
        {
          name: 'Kabupaten',
          value: 'Kabupaten',
        },
        {
          name: 'Kecamatan',
          value: 'Kecamatan',
        },
        {
          name: '<PERSON><PERSON><PERSON><PERSON>',
          value: '<PERSON><PERSON><PERSON><PERSON>',
        },
        {
          name: '+ RT',
          value: 'JmlRT',
        },
        {
          name: '+ K<PERSON>',
          value: 'JmlKK',
        },
        {
          name: '+ RTLH',
          value: 'JmlRTLH',
        },
        {
          name: '+ Backlog',
          value: 'JmlBacklog',
        },
      ]"
      :doPrint="doPrint"
      @onPrint="Print"
    >
    </Grid>
    <DBMap
      v-show="true"
      dbref="BCN.SelKebencanaanMap"
      :dbparams="filters"
      :noRef="selectedRef"
      :disabled="true"
      v-model:value="mapValue"
      @marker-click="MarkerClick"
    />
    <BencanaDetail
      v-model:show="showDetailModal"
      :id="kebencanaanId"
      :geoLoc="mapValue"
    />
    <ReportViewer v-model:options="reportOptions" :show="showReport" />
  </Page>
</template>
<script>
import ReportViewer from '../../../components/ReportViewer.vue'
import BencanaDetail from './BencanaDetail.vue'
import DBMap from './Map.vue'
export default {
  components: {
    BencanaDetail,
    ReportViewer,
    DBMap,
  },
  data: () => ({
    showDetailModal: false,
    kebencanaanId: 0,
    filters: {},
    selectedRef: null,
    backlog: [],
    reportUrl: null,
    mapValue: null,
    showReport: false,
    doPrint: 0,
    reportOptions: {},
  }),
  async mounted() {},
  watch: {
    mapValue(val) {
      if (val) this.showDetailModal = true
    },
  },
  methods: {
    OpenDetail(noRef) {
      this.selectedRef = noRef
      this.showDetailModal = true
    },
    Print(headers, data) {
      this.reportOptions = {
        headers: headers,
        data: data,
      }
      this.showReport = true
    },
    MarkerClick(data) {
      this.kebencanaanId = data.KebencanaanID
      this.showDetailModal = true
    },
  },
}
</script>
<style lang="scss">
.table-desc {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;

  div {
    padding: 3px 5px;
    margin-left: 3px;
    color: white;
  }
}
</style>
