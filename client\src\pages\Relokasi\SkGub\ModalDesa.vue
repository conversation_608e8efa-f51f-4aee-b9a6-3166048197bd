<template>
  <Modal
    :title="params.Kabupaten"
    v-model:show="xshow"
    width="400px"
    @onSubmit="Save"
  >
    <div>
      <div
        style="padding: 10px 0; display: flex; font-size: small; width: 590px"
      >
        <div style="width: 100%">
          <Checkbox
            v-model:value="hasAllocation"
            style="margin-right: 10px"
            text="Hanya yang memiliki alokasi"
          />
        </div>
      </div>
      <div
        style="display: flex; font-size: small; width: 590px; margin-top: -10px"
      >
        <XInput
          type="text"
          v-model:value="keyword"
          :rightIcon="keyword ? 'mdi-close' : 'search'"
          width="345px"
        />
        <div class="top-chips">
          {{ alokasi.tahap1 }}
        </div>
        <div class="top-chips">
          {{ alokasi.tahap2 }}
        </div>
      </div>
      <Grid
        dbref="RLK.AlokasiSkGub"
        :dbparams="params"
        v-model:datagrid="datagrid"
        :filter="filterGrid"
        :disabled="true"
        :autopaging="false"
        height="calc(80vh - 100px)"
        :columns="[
          {
            name: 'Kecamatan',
            value: 'Kecamatan',
            width: '150px',
          },
          {
            name: 'Desa',
            value: 'Kelurahan',
            width: '150px',
          },
          {
            name: 'Reguler',
            value: 'Tahap1',
            class: 'plain',
          },
          {
            name: 'Perubahan',
            value: 'Tahap2',
            class: 'plain',
          },
        ]"
      >
        <template #row-Tahap1="{ row }">
          <XInput
            type="number"
            v-model:value="row.Tahap1"
            width="95px"
            style="margin-left: 5px"
          />
        </template>
        <template #row-Tahap2="{ row }">
          <XInput
            type="number"
            v-model:value="row.Tahap2"
            width="95px"
            style="margin-left: 5px"
          />
        </template>
      </Grid>
    </div>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    xshow: false,
    // alokasi: 0,
    datagrid: [],
    hasAllocation: false,
    keyword: '',
  }),
  props: {
    show: Boolean,
    params: Object,
  },
  computed: {
    alokasi() {
      let tahap1 = 0
      let tahap2 = 0
      let tahap3 = 0
      let tahap4 = 0
      let tahap5 = 0
      let tahap6 = 0
      for (let d of this.datagrid) {
        tahap1 += parseInt(d.Tahap1) || 0
        tahap2 += parseInt(d.Tahap2) || 0
        tahap3 += parseInt(d.Tahap3) || 0
        tahap4 += parseInt(d.Tahap4) || 0
        tahap5 += parseInt(d.Tahap5) || 0
        tahap6 += parseInt(d.Tahap6) || 0
      }
      return { tahap1, tahap2, tahap3, tahap4, tahap5, tahap6 }
    },
    jmlDesa() {
      return this.datagrid.filter((d) => d.Tahap1 > 0).length
    },
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      this.$emit('update:show', val)
    },
  },
  methods: {
    async Save() {
      let ret = await this.$api.call('RLK_SavAlokasiSkGubDes', {
        ...this.params,
        XmlAlokasi: this.datagrid,
      })
      if (ret.success) {
        this.$emit('update:show', false)
        this.$emit('close', this.alokasi)
      }
    },
    filterGrid(row) {
      let hasAlloc = this.hasAllocation
        ? Boolean(
            row.Tahap1 ||
              row.Tahap2 ||
              row.Tahap3 ||
              row.Tahap4 ||
              row.Tahap5 ||
              row.Tahap6
          )
        : true

      let rx = new RegExp(this.keyword, 'i')
      let hasKey =
        !this.keyword || row.Kecamatan.match(rx) || row.Kelurahan.match(rx)

      return hasAlloc && Boolean(hasKey)
    },
  },
}
</script>
<style lang="scss">
.top-chips {
  min-width: 80px;
  text-align: right;
  background: #ddd;
  border-radius: 5px;
  padding: 5px;
  height: 30px;
  margin-left: 5px;
}
</style>
