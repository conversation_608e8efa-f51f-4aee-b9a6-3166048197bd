import { createApp } from 'vue'
import App from './App.vue'
import store from './store'
import router from './router'
import componentDefault from './plugins/default'
import api from './api'
import vuetify from './plugins/vuetify' // Import the created vuetify instance
import 'floating-vue/dist/style.css' // Import floating-vue styles
// import FloatingVue from 'floating-vue' // Import FloatingVue
// import VueToast from 'vue-toast-notification' // This will need to be updated for Vue 3
import { registerSW } from 'virtual:pwa-register'
import Vue3Toastify from 'vue3-toastify'
import moment from 'moment'
import PrimeVue from 'primevue/config'
import Aura from '@primeuix/themes/aura'

registerSW({
  immediate: true,
  onNeedRefresh() {
    console.log('Need Refresh')
  },
  onOfflineReady() {
    console.log('Offline Ready')
  },
})

// import './assets/main.css'
import 'material-design-icons-iconfont/dist/material-design-icons.css'
import 'vue-toast-notification/dist/theme-sugar.css' // Keep the toast notification styles

// Vue.config.productionTip = false // Removed in Vue 3

const app = createApp(App)
app.use(PrimeVue, {
  theme: {
    preset: Aura,
    options: {
      darkModeSelector: false || 'none',
    },
  },
})

// Vue.use(Vuetify) // Replaced by app.use()
// Vue.use(VTooltip) // Replaced by app.use()
// Vue.use(VueToast) // Replaced by app.use()
componentDefault.register(app) // Need to check if this plugin needs the app instance

// Vue.prototype.$api = api // Replaced by app.config.globalProperties
app.config.globalProperties.$api = api

// Vue.mixin({...}) // Still supported, but Composition API is recommended
app.mixin({
  computed: {
    isMobile: () => {
      return window.innerWidth < window.innerHeight ? 'is-mobile' : ''
    },
    AllowWrite: () => {
      return Boolean(
        (window._rwx.match && window._rwx.match(/w/)) || window._rwx
      )
    },
    AllowDelete: () => {
      return Boolean(
        (window._rwx.match && window._rwx.match(/x/)) || window._rwx
      )
    },
  },
})

app.config.globalProperties.$filters = {
  format(val, f) {
    if (!isNaN(val)) {
      return String(val).replace(/(.)(?=(\d{3})+$)/g, '$1.')
    } else if (String(val).match(/\d{4}-\d{2}-\d{2}/)) {
      if (f == 'fromNow') return moment(val).locale('id').fromNow(true)
      else return moment(val).format(f ? f : 'DD-MMM-YYYY')
    }
    return val
  },
}

// Vue.config.errorHandler = (err) => { // Replaced by app.config.errorHandler
// app.config.errorHandler = (err) => {
//   console.error(err)

//   if (window.location.hostname == 'localhost') return
//   let popup = document.querySelector(
//     '.v-dialog--active .v-card__title.headline'
//   )
//   if (popup) popup = ' (' + popup.innerHTML + ')'
//   else popup = ''

//   api.post('/api/log', {
//     Source: 'FE',
//     UserId: null,
//     PageRef: window.location.href + popup,
//     Details: err.stack,
//   })
// }
app.use(router)
app.use(store)
app.use(vuetify) // Use the created vuetify instance
// app.use(FloatingVue) // Use FloatingVue
app.use(Vue3Toastify, {
  autoClose: 3000,
})

app.mount('#app')
