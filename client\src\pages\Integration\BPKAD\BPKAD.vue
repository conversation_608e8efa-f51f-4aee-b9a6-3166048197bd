<template>
  <div class="form-inline" style="padding: 20px; height: calc(100vh - 40px)">
    <XSelect
      label="Tahun"
      dbref="PRM.SelProposal"
      v-model:value="forms.Tahun"
      :valueAsObject="true"
      @change="PopulateBerkas"
    />
    <XSelect
      label="Rekom"
      v-model:value="forms.Rekom"
      dbref="PRM.SelRekom"
      @change="PopulateBerkas"
    />
    <XSelect
      label="Kabupaten"
      v-model:value="forms.KabupatenID"
      :items="kabupatens"
      @change="PopulateBerkas"
    />
    <XInput label="No. Surat" v-model:value="forms.NoSurat" />
    <XInput label="Perihal" v-model:value="forms.Perihal" />
    <XInput label="Tgl. Surat" v-model:value="forms.TglSurat" />
    <div>
      <div>
        <Uploader
          label="SURAT REKOM"
          v-model:value="forms.SuratRekom"
          accept=".pdf"
        ></Uploader>
        <Uploader
          label="VERIFIKASI SKPD"
          v-model:value="forms.VerifikasiSKPD"
          accept=".pdf"
        >
        </Uploader>
        <Uploader label="KWITANSI" v-model:value="forms.Kwitansi" accept=".pdf">
        </Uploader>
        <Uploader
          label="BUKU REKENING"
          v-model:value="forms.BukuRekening"
          accept=".pdf"
        ></Uploader>
      </div>
      <div>
        <Uploader
          label="KTP KADES/ BENDAHARA"
          v-model:value="forms.KtpKades"
          accept=".pdf"
        ></Uploader>
        <Uploader
          label="LAMPIRAN REKOM"
          v-model:value="forms.LampiranRekom"
          accept=".xls,.xlsx"
        ></Uploader>
        <Uploader
          label="BNBA RTLH"
          v-model:value="forms.BNBARTLH"
          accept=".xls,.xlsx"
        ></Uploader>
      </div>
    </div>
    <br />
    <v-btn color="primary" @click="Submit">SUBMIT</v-btn>
  </div>
</template>
<script>
import moment from 'moment'
export default {
  data: () => ({
    forms: {
      NoSurat: '',
      Perihal: '',
      TglSurat: moment().format('YYYY-MM-DD'),
      KabupatenID: '',
      KtpKades: '',
      Kwitansi: '',
      BukuRekening: '',
    },
    kabupatens: [
      { id: '7', nama: 'Kabupaten Semarang' },
      { id: '8', nama: 'Kabupaten Demak' },
      { id: '9', nama: 'Kabupaten Kudus' },
      { id: '10', nama: 'Kabupaten Jepara' },
      { id: '11', nama: 'Kabupaten Pati' },
      { id: '12', nama: 'Kabupaten Rembang' },
      { id: '13', nama: 'Kabupaten Blora' },
      { id: '14', nama: 'Kabupaten Grobogan' },
      { id: '15', nama: 'Kabupaten Wonosobo' },
      { id: '16', nama: 'Kabupaten Banjarnegara' },
      { id: '17', nama: 'Kabupaten Purbalingga' },
      { id: '18', nama: 'Kabupaten Banyumas' },
      { id: '19', nama: 'Kabupaten Cilacap' },
      { id: '20', nama: 'Kabupaten Kebumen' },
      { id: '21', nama: 'Kabupaten Purworejo' },
      { id: '22', nama: 'Kabupaten Brebes' },
      { id: '23', nama: 'Kabupaten Boyolali' },
      { id: '24', nama: 'Kabupaten Klaten' },
      { id: '25', nama: 'Kabupaten Sukoharjo' },
      { id: '26', nama: 'Kabupaten Wonogiri' },
      { id: '27', nama: 'Kabupaten Karanganyar' },
      { id: '28', nama: 'Kabupaten Sragen' },
      { id: '29', nama: 'Kabupaten Magelang' },
      { id: '30', nama: 'Kabupaten Temanggung' },
      { id: '31', nama: 'Kabupaten Tegal' },
      { id: '32', nama: 'Kabupaten Pemalang' },
      { id: '33', nama: 'Kabupaten Pekalongan' },
      { id: '34', nama: 'Kabupaten Batang' },
      { id: '35', nama: 'Kabupaten Kendal' },
    ],
  }),
  mounted() {
    this.forms.TglSurat = moment().format('YYYY-MM-DD') + ' '
  },
  methods: {
    async PopulateBerkas() {
      let kab = this.kabupatens.find((k) => {
        return k.id == this.forms.KabupatenID
      })
      if (kab) {
        let ret = await this.$api.get(
          '/reports/others/berkas-penyaluran?' +
            [
              `_PRM_SelKabupatenID=${kab.nama.replace('Kabupaten ', '')}`,
              `_PRM_SelProposalID=${this.forms.Tahun.ProposalID}`,
              `_PRM_SelRekomID=${this.forms.Rekom}`,
            ].join('&')
        )
        if (ret.success) {
          let d = ret.data
          this.forms.KtpKades = d.ktpKades
          this.forms.Kwitansi = d.kwitansi
          this.forms.BukuRekening = d.bukuRekening
        }
      }
    },
    async Submit() {
      let ret = await this.$api.post('/api/bpkad/submit', this.forms)
      this.$api.notify(ret.message, ret.success ? 'success' : 'error')
    },
  },
}
</script>
