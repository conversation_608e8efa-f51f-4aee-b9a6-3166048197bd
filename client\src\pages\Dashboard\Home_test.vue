<template>
  <Page title="Dashboard">
    <div
      style="position: absolute; width: 100vw; text-align: center; top: 55px"
      v-if="hasRTLH && hasBacklog"
    >
      <v-btn variant="text" style="font-weight: bold" rounded v-if="hasRTLH">RTLH</v-btn>
      <v-btn rounded @click="GoToBacklog" v-if="hasBacklog">Backlog</v-btn>
    </div>
    <div
      style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 90px)"
    >
      <ChartRealisasiKab sumber="2" />
      <div style="height: 150px"></div>
      <ChartRealisasiKab sumber="0" />
      <div style="height: 150px"></div>
      <ChartValidasiKab tipedata="115" />
      <div style="height: 170px"></div>
      <div
        style="
          height: 100px;
          background: rgba(255, 255, 255, 0.5);
          display: flex;
          justify-content: center;
        "
      >
        <div
          style="
            display: flex;
            width: 40vw;
            align-self: center;
            text-align: center;
          "
        >
          <a
            href="http://datartlh.perumahan.pu.go.id/"
            target="_blank"
            style="flex: 1"
          >
            <img src="/imgs/ertlh.png" height="30px" />
          </a>

          <a
            href="https://sidesa.jatengprov.go.id/"
            target="_blank"
            style="flex: 1"
          >
            <img
              src="https://sidesa.jatengprov.go.id/img/onscreen/home/<USER>"
              height="30px"
            />
          </a>

          <a
            href="https://caribdt.dinsos.jatengprov.go.id/public/dashboard"
            target="_blank"
            style="flex: 1"
          >
            <img src="/imgs/caribdt-min.png" height="30px" />
          </a>
        </div>
      </div>
    </div>
  </Page>
</template>

<script>
// @ is an alias to /src
import LastActivities from './LastActivities.vue'
import Rekap from './Rekap.vue'
import ChartRealisasiKab from './ChartRealisasiKab.vue'
import ChartValidasiKab from './ChartValidasiKab.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'Home',
  components: {
    LastActivities,
    Rekap,
    ChartRealisasiKab,
    ChartValidasiKab,
  },
  data: () => ({
    tipeData: '115',
  }),
  computed: {
    ...mapGetters({
      user: 'getUser',
    }),
    hasBacklog() {
      let menu = JSON.parse(localStorage.getItem('menu'))
      return menu.find((m) => {
        return m.MenuName === 'Backlog'
      })
    },
    hasRTLH() {
      let menu = JSON.parse(localStorage.getItem('menu'))
      return (menu || []).find((m) => {
        return m.MenuName === 'RTLH'
      })
    },
  },
  mounted() {
    if (!this.hasRTLH) this.$router.push('/Main/Backlog/Dashboard')
  },
  methods: {
    GoToBacklog() {
      this.$router.push('/Main/Backlog/Dashboard')
    },
  },
}
</script>
<style lang="scss">
.hide {
  display: none;
}
</style>
