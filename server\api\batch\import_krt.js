var mysql = require("mysql");
var util = require("util");
var axios = require("axios");
const fs = require("fs");
const {query} = require("../../common/db");
//const readline = require("readline");
// var readlines = require("n-readlines");
// const stream = require("stream");

// const colmap = {
//   NIK: "NIK",

// }

const kabs = [
  "CIL<PERSON><PERSON>",
  "BANYUMAS",
  "PURBALINGGA",
  "BANJARNEGARA",
  "KEBUMEN",
  "PURWOREJO",
  "W<PERSON><PERSON><PERSON><PERSON>",
  "MAGELANG",
  "BOYOLALI",
  "K<PERSON>TE<PERSON>",
  "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  "KARANGAN<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "GROBOGA<PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>",
  "REMBANG",
  "PATI",
  "KUDUS",
  "JEPARA",
  "DEMAK",
  "SEMARANG",
  "TEM<PERSON><PERSON><PERSON><PERSON><PERSON>",
  "K<PERSON><PERSON>L",
  "BATA<PERSON>",
  "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
  "PEMALANG",
  "TEGAL",
  "BR<PERSON><PERSON><PERSON>",
  "KOTA MAGE<PERSON><PERSON>",
  "KOTA SURAKARTA",
  "KOTA SALATIGA",
  "KOTA SEMARANG",
  "KOTA PEKALONGAN",
  "KOTA TEGAL",
];
var failed = "";
var pool = mysql.createPool({
  connectionLimit: 10,
  // host: "*************",
  host: '*************',
  user: "devusr",
  password: "Simperum2019!",
  database: "perum",
});

pool.getConnection((err, connection) => {
  if (err) {
    if (err.code === "PROTOCOL_CONNECTION_LOST") {
      console.error("Database connection was closed.");
    }
    if (err.code === "ER_CON_COUNT_ERROR") {
      console.error("Database has too many connections.");
    }
    if (err.code === "ECONNREFUSED") {
      console.error("Database connection was refused.");
    }
  }
  if (connection) connection.release();
  return;
});
pool.query = util.promisify(pool.query); // Magic happens here.

async function getKodeDagri(kab) {
  var kodedagris = [];
  //   var res = await pool.query(
  //     `SELECT KelurahanID, KodeDagri, Kelurahan FROM arch_varea av WHERE Kabupaten = '${kab}'
  //       AND KodeDagri NOT IN (
  //         SELECT DISTINCT KodeWilayah FROM prm_pbdt pp
  //         WHERE Kabupaten = '${kab}' AND TipeData = 121
  //       )
  //       ORDER BY KodeDagri`
  //   );
  var res = await pool.query(
    `SELECT KelurahanID, KodeDagri, Kelurahan FROM arch_varea av WHERE Kabupaten = '${kab}'
      ORDER BY KodeDagri`
  );

  // var res = await pool.query(
  //   `SELECT KelurahanID, KodeDagri, Kelurahan FROM arch_varea av WHERE Kabupaten = 'SUKOHARJO' AND Kelurahan = 'PLESAN'
  //     ORDER BY KodeDagri`
  // );
  for (var i in res) {
    // var k = res[i].KodeDagri + "";
    // kodedagris.push(
    //   k.substr(0, 2) +
    //     "." +
    //     k.substr(2, 2) +
    //     "." +
    //     k.substr(4, 2) +
    //     "." +
    //     k.substr(6, 4)
    // );
    var k = res[i].KodeDagri + "";
    kodedagris.push({
      all: k,
      nmkel: res[i].Kelurahan,
      dagri: res[i].KodeDagri + "",
      prov: k.substr(0, 2),
      kab: k.substr(2, 2),
      kec: k.substr(4, 2),
      desa: k.substr(-4),
    });
  }
  return kodedagris;
}

async function importPBDT(kodedagri) {
  process.stdout.write(`\nImporting to PBDT.. `);
  let query = `
    INSERT IGNORE INTO prm_pbdt (NoRef, NIK, NoKK, Nama, Alamat, Kabupaten, Kecamatan, Kelurahan, KodeWilayah,
      AtapID, KondisiAtap, LantaiID, KondisiLantai, DindingID, KondisiDinding, KepemilikanLahan, KepemilikanRumah, 
      Penerangan, SumberAir, KamarMandi, LuasRumah,
      Persentil, TipeData 
    )
    SELECT rb.IDBDT, rb.NIK, rb.NoKK, rb.Nama_KRT, rb.Alamat, COALESCE(rb.NMKAB, a.Kabupaten), COALESCE(rb.NMKEC, a.Kecamatan), COALESCE(rb.NMDESA,a.Kelurahan), rb.KodeDagri,  
      NULLIF(rb.atap,0), NULLIF(rb.kondisi_atap,0), NULLIF(rb.lantai,0), NULL, NULLIF(rb.dinding,0), NULLIF(rb.kondisi_dinding,0), sta_lahan, sta_bangunan,
      rb.sumber_penerangan, rb.sumber_airminum, rb.fasbab, rb.luas_lantai,
      rb.percentile, 121 
    FROM raw_dtks2022 rb 
      LEFT JOIN prm_pbdt p
      ON rb.NIK = p.NIK
      JOIN arch_varea a
      ON rb.KodeDagri = a.KodeDagri
    WHERE rb.KodeDagri = ${kodedagri.replace(/\./g, "")} 
    AND rb.NIK IS NOT NULL AND p.NIK IS NULL;`;

  var res = await pool.query(query).catch(function (err) {
    console.error("Error while inserting PBDT");
    console.error(err.sqlMessage);
    fs.writeFileSync("perum/query.log", query);
  });

  query = `
    UPDATE prm_pbdt p
        JOIN raw_dtks2022 rb ON 
        p.NIK = rb.NIK 
    SET p.TipeData = 121
    WHERE p.TipeData BETWEEN 200 AND 300 
    AND rb.KodeDagri = ${kodedagri.replace(/\./g, "")}`;
    
  res = await pool.query(query).catch(function (err) {
    console.error("Error while inserting PBDT");
    console.error(err.sqlMessage);
    fs.writeFileSync("query.log", query);
  });
  if (res) process.stdout.write(`done (${res.affectedRows})`);
}

async function importkrt(kd) {
  var ret = await axios
    .get(
      "https://caribdt.dinsos.jatengprov.go.id/api/desa_perakim9502/?username=disperakim&token=eIJCVbmZfYdusKiqqVDN&" +
        `kdkab=${kd.kab}&kdkec=${kd.kec}&kddesa=${kd.desa}`)
    .catch((err) => {
      return "retry";
    });
  if (ret && ret.data) {
    // fs.writeFileSync("./perum/import_krt.csv", JSON.stringify(ret.data));

    var cols = ret.data[0];
    // console.log(cols);
    // BUILD Query
    var query = "INSERT IGNORE INTO raw_dtks2022 (";
    for (var col in cols) {
      query += `${col}, `;
    }
    query += `TipeData, KodeDagri, imported_date)\nVALUES `;

    var data = ret.data;
    for (var i in data) {
      query += `(`;
      for (var d in data[i]) {
        if (
          data[i][d] &&
          ((data[i][d].replace &&
            data[i][d].replace(/[^a-z]/gi, "") === "NULL") ||
            (data[i][d].trim && data[i][d].trim() === "-"))
        )
          query += "NULL,";
        else
          query += `TRIM(${mysql.escape(
            data[i][d] && data[i][d].trim ? data[i][d].trim() : data[i][d]
          )}), `;
      }
      query += `122, ${kd.dagri.replace(/\./g, "")}, NOW()),\n`;
    }
    query = query.replace(/,\n$/g, "");
    // query += `\nON DUPLICATE KEY UPDATE\n`;
    // for (let col in cols) {
    //   if (col !== "IDBDT") query += `${col} = VALUES(${col}),`;
    // }
    // query += `updated_date = NOW(), 
    //           KodeDagri = ${kd.dagri.replace(/\./g, "")}`;
    // console.log(query);
    var res = await pool.query(query).catch(function (err) {
      console.error(err.sqlMessage);
      fs.writeFileSync("query.log", query);
      fs.appendFileSync("failed_import_krt.log", kd.dagri + "\n");
      // process.exit(0);
      return "error";
    });
    // process.exit(0);
    process.stdout.write(`done (${res.affectedRows})`);
    return "";
  } else {
    process.stdout.write(`retry.. `);
    return "retry";
  }
}

const sleep = (milliseconds) => {
  return new Promise((resolve) => setTimeout(resolve, milliseconds));
};

async function main() {
  for (var k in kabs) {
    var kds = await getKodeDagri(kabs[k]);
    console.log(kds.length);
    var skip = false;
    var ret = "";
    for (var i in kds) {
      if (!skip) {
        process.stdout.write(
          `\nimporting ${kabs[k]},${kds[i].nmkel}: ${kds[i].dagri}.. `
        );
        ret = await importkrt(kds[i]);
        if (ret === "retry") {
          await sleep(5000);
          await importkrt(kds[i]);
        }
        // if (!ret) 
        // await importkrt(kds[i].dagri);
      }
      // 33.01.03.2009
      // if (kds[i] == "33.06.01.2026") skip = false;
    }
  }
  process.exit(0);
}

async function updateNIK() {
  for (var k in kabs) {
    var kds = await getKodeDagri(kabs[k]);
    console.log(kds.length);
    for (var i in kds) {
      process.stdout.write(
        `\nupdating ${kabs[k]},${kds[i].nmkel}: ${kds[i].dagri}.. `
      );
      let query = `update prm_pbdt set ModifiedDate = NOW()
            where SkorBKEU >= 60 AND ModifiedDate IS NULL AND KodeWilayah = '${kds[i].dagri}'`
      var res = await pool.query(query).catch(function (err) {
        console.log(err.sqlMessage);
        return "error";
      });
      process.stdout.write(
        `done`
      );
    }
  }
}

// getKodeDagri();
main();
// updateNIK();
// importkrt({
//   kab: 33,
//   kec: 11,
//   desa: "0011",
// });
// importPBDT("33.13.17.2007");

