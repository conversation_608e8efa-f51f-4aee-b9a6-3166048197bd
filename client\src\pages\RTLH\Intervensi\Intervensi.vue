<template>
  <Page title="Data Intervensi">
    <template v-slot:toolbar>
      <v-btn size="small" @click="showRolesPage = true">+ Role</v-btn>
    </template>
    <Grid
      v-model:datagrid="users"
      dbref="PRM.Intervensi"
      :disabled="true"
      style="height: calc(100vh - 120px)"
      class="dense"
      :columns="[
        {
          name: 'NIK',
          value: 'NIK',
        },
        {
          name: '<PERSON><PERSON>',
          value: '<PERSON><PERSON>',
        },
        {
          name: '<PERSON><PERSON><PERSON>',
          value: '<PERSON>ama<PERSON>',
        },
        {
          name: 'Intervensi',
          value: 'Sumber',
        },
        {
          name: '<PERSON><PERSON>',
        },
        {
          name: 'BDT',
          value: 'NoRef',
        },
      ]"
    >
    </Grid>
  </Page>
</template>
<script>
export default {
  components: {},
  data: () => ({
    roleAccess: false,
    showRolesPage: false,
    showAreaAccess: false,
    roleAccessData: [],
    rolesData: [],
    areaAccessData: [],
    roleId: null,
    userId: null,
    users: null,
    forms: {},
  }),
  async mounted() {},
  methods: {
    ShowRoleAccess(roleId) {
      this.roleAccess = true
      this.roleId = roleId
    },
    ShowAreaAccess(userId) {
      this.showAreaAccess = true
      this.userId = userId
    },
    async SubmitRoles() {},
    async SubmitRoleAccess() {
      let ret = this.$api.call('Arch.SavRoleAccess', {
        RolePositionID: this.roleId,
        XmlRoleAccess: this.roleAccessData,
      })
      if (ret.success) this.roleAccess = false
    },
    async SubmitAreaAccess() {
      let ret = await this.$api.call('Arch.SavUserArea', {
        UserID: this.userId,
        Remarks: this.areaAccessData
          .filter((a) => a.AllowAccess)
          .map((a) => a.AreaID)
          .join(','),
      })

      if (ret.success) this.showAreaAccess = false
    },
  },
}
</script>
<style lang="scss">
#modal-role-access {
  .ui-table {
    height: 500px !important;
  }
}
</style>
