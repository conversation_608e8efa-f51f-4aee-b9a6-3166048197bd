<template>
  <Page title="Database Kebencanaan">
    <template v-slot:toolbar>
      <v-icon @click="doPrint++">print</v-icon>
    </template>
    <Grid
      v-model:datagrid="backlog"
      dbref="BCN.BNBA"
      :dbparams="filters"
      :disabled="true"
      :height="'calc(100vh - 120px)'"
      :columns="[
        {
          name: 'NIK',
          value: 'NIK',
          filter: {
            type: 'search',
            value: 'Keyword',
          },
        },
        {
          name: '<PERSON><PERSON>',
          value: '<PERSON>a',
          filter: {
            type: 'search',
            value: 'Keyword',
          },
        },
        {
          name: '<PERSON><PERSON><PERSON>',
          value: '<PERSON>ama<PERSON>',
        },
        {
          name: 'Kabupaten',
          value: 'Kabupaten',
          filter: {
            type: 'select',
            value: 'KabupatenID',
            text: 'Kabupaten',
            dbref: 'Arch.SelArea',
            dbparams: { ParentAreaID: 33 },
          },
        },
        {
          name: 'Kecamatan',
          value: 'Kecamatan',
          filter: {
            type: 'select',
            value: 'KecamatanID',
            text: 'Kecamatan',
            dbref: 'Arch.SelArea',
            dbparams: f => ({ ParentAreaID: f.KabupatenID }),
          },
        },
        {
          name: 'Kelurahan',
          value: 'Kelurahan',
          filter: {
            type: 'select',
            value: 'KelurahanID',
            text: 'Kelurahan',
            dbref: 'Arch.SelArea',
            dbparams: f => ({ ParentAreaID: f.KecamatanID }),
          },
        },
        {
          name: 'IDBDT',
          value: 'IDBDT',
        },
      ]"
      :doPrint="doPrint"
      @onPrint="Print"
    >
    </Grid>
  </Page>
</template>
<script>
export default {
  components: {},
  data: () => ({
    showDetailModal: false,
    filters: {},
    selectedRef: null,
    backlog: [],
    reportUrl: null,
    mapValue: null,
    showReport: false,
    doPrint: 0,
    reportOptions: {},
  }),
  async mounted() {},
  watch: {
    mapValue(val) {
      if (val) this.showDetailModal = true
    },
  },
  methods: {
    OpenDetail(noRef) {
      this.selectedRef = noRef
      this.showDetailModal = true
    },
    Print(headers, data) {
      this.reportOptions = {
        headers: headers,
        data: data,
      }
      this.showReport = true
    },
  },
}
</script>
<style lang="scss">
.table-desc {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;

  div {
    padding: 3px 5px;
    margin-left: 3px;
    color: white;
  }
}
</style>
