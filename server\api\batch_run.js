var db = require('../common/db')
var ertlh = require('./thirdparty/ertlh')

const xmethods = {
  async import(kd) {
    let kab = []
    if (kd == 'kota') {
      kab = await db.query(`SELECT Kabupaten, Kecamatan , Kelurahan , KodeDagri FROM arch_varea av 
              WHERE Kabupaten LIKE 'PATI'
              ORDER BY 4;`)
    } else {
      kab = await db.query(`SELECT Kabupaten, Kecamatan , Kelurahan , KodeDagri FROM arch_varea av 
              WHERE KodeDagri = ${kd};`)
    }
    for (let i = 0; i < kab.length; i++) {
      // console.log(kab[i]);
      let data = await ertlh.read(kab[i].KodeDagri)
      let sqlinsert = 'REPLACE INTO raw_ertlh ('
      if (data.length && data.forEach) {
        // console.log(data[0])
        // console.log(`inserting ${data.length} data`)
        sqlinsert += Object.keys(data[0]).join(',') + ') VALUES '
        data.forEach((d) => {
          if (Number.isSafeInteger(parseInt(d.nik))) {
            d.nik = parseInt(d.nik)
            sqlinsert += (
              "('" +
              Object.values(d)
                .join('|')
                .replace(/'/g, '')
                .replace(/\|/g, "','") +
              "'),"
            )
              .replace(/,''/g, ',NULL')
              .replace(/,'\\',/g, ',NULL,')
              .replace(/\\','/g, "','")
          }
        })
      }
      let ret = await db.query(sqlinsert.replace(/,$/, ';'))
      if (ret.length && ret[0].length) {
        // console.log(sqlinsert)
      }
    }
  },
  async export(kd, withCheck) {
    let kab = []
    let sql = 'SELECT 1'
    if (kd == 'test') {
      sql = `SELECT p.NIK, p.Kabupaten, p.KodeWilayah FROM prm_pbdt p
              LEFT JOIN raw_ertlh e
              ON p.NIK = e.NIK
            WHERE e.nik IS NULL AND p.NIK IS NOT NULL AND p.VerStatsID >= 6 -- AND p.SkorKelayakan < 500 
              AND Kabupaten = 'KENDAL' AND KELURAHAN IN ('SARIREJO', 'KRAJAN KULON')
            ORDER BY p.KodeWilayah
            LIMIT 50000
            `
    } else if (kd == 'bsps') {
      sql = `SELECT p.NIK, p.Kabupaten, p.KodeWilayah FROM prm_proposaldet p
          JOIN prm_pbdt d
          ON p.NIK = d.NIK 
          LEFT JOIN raw_ertlh e
          ON p.NIK = e.NIK
        WHERE p.Sumber IN (1,9) AND p.Tahun = YEAR(NOW()) AND e.NIK IS NULL AND d.SkorKelayakan < 500
        ORDER BY p.KodeWilayah;`
    } else {
      sql = `SELECT p.NIK, p.Kabupaten, p.KodeWilayah FROM prm_pbdt p
          LEFT JOIN raw_ertlh e
          ON p.NIK = e.NIK
        WHERE p.Kabupaten = '${kd}' AND p.VerStatsID = 6 AND e.NIK IS NULL AND p.NIK IS NOT NULL -- AND SkorKelayakan < 500
        ;`
    }
    kab = await db.query(sql)
    let lastKab = ''
    let err = []
    for (let i = 0; i < kab.length; i++) {
      if (lastKab != kab[i].KodeWilayah) {
        if (lastKab !== '') {
          if (withCheck) {
            // xmethods.check(sql, lastKab);
            err = await xmethods.check(sql, lastKab)
            // if (err.length > 0) break;
          }
        }
        console.log(kab[i].Kabupaten, kab[i].KodeWilayah)
        lastKab = kab[i].KodeWilayah
      }
      console.log(`${i}/${kab.length}`, kab[i].NIK)
      await ertlh.insert(kab[i].NIK)
    }
    if (withCheck && kab.length) {
      await xmethods.check(sql, lastKab)
    }
    process.exit(0)
  },
  async check(sql, kd) {
    console.log(`Checking ${kd} ...`)
    await xmethods.import(kd)
    let kab = await db.query(sql)
    if (kd) kab = kab.filter(k => k.KodeWilayah == kd)
    console.log(`Sisa ${kd}: ${kab.length}`)
    return kab
  },
  async checkOnly(kd, withCheck) {
    let kab = []
    let sql = 'SELECT 1'
    if (kd == 'test') {
      sql = `SELECT DISTINCT p.Kelurahan NIK, p.Kabupaten, p.KodeWilayah FROM prm_pbdt p
              LEFT JOIN raw_ertlh e
              ON p.NIK = e.NIK
            WHERE p.Kabupaten IN ('SUKOHARJO') AND e.nik IS NULL AND p.SkorKelayakan < 500 AND p.NIK IS NOT NULL AND p.VerStatsID = 6
            `
    } else if (kd == 'bsps') {
      sql = `SELECT p.NIK, p.Kabupaten, p.KodeWilayah FROM prm_proposaldet p
          JOIN prm_pbdt d
          ON p.NIK = d.NIK 
          LEFT JOIN raw_ertlh e
          ON p.NIK = e.NIK
        WHERE p.Sumber IN (1,9) AND p.Tahun = YEAR(NOW()) AND e.NIK IS NULL AND d.SkorKelayakan < 500
        ORDER BY p.KodeWilayah;`
    } else {
      sql = `SELECT p.NIK, p.Kabupaten, p.KodeWilayah FROM prm_pbdt p
          LEFT JOIN raw_ertlh e
          ON p.NIK = e.NIK
        WHERE p.KodeWilayah = ${kd} AND p.VerStatsID = 6 AND e.NIK IS NULL AND SkorKelayakan < 500;`
    }
    kab = await db.query(sql)
    let lastKab = ''
    let err = []
    for (let i = 0; i < kab.length; i++) {
      if (lastKab != kab[i].KodeWilayah) {
        if (lastKab !== '') {
          if (withCheck) {
            // xmethods.check(sql, lastKab);
            err = await xmethods.check(sql, lastKab)
            //  if (err.length > 0) break;
          }
        }
        console.log(kab[i].Kabupaten, kab[i].KodeWilayah)
        lastKab = kab[i].KodeWilayah
      }
      console.log(`${i}/${kab.length}`, kab[i].NIK)
      // await ertlh.insert(kab[i].NIK);
    }
    if (withCheck && !err.length) {
      await xmethods.check(sql, lastKab)
    }
  },
}

async function main() {
  await xmethods.export("SRAGEN", true);
  // await xmethods.checkOnly('KENDAL', true)
}
main()
