<template>
  <div>
    <div class="grid-mobile">
      <div class="--header">
        <div>Header</div>
        <v-spacer />
        <div>
          <v-icon v-if="!disabled" @click="handleAddNew">mdi-plus</v-icon>
        </div>
      </div>
      <div class="--body">
        <div
          v-for="(row, idx) in datagrid"
          :key="idx"
          class="--row"
          @click="OpenDet(idx, row, $event)"
        >
          <div
            v-for="(col, idxcol) in colpos"
            :key="idxcol"
            :class="col.mobilePos || 'no-pos'"
          >
            <slot
              :name="'row-' + col.value"
              :row="row"
              :idx="idx"
              :isMobile="true"
              v-if="!col.hide"
            >
              <div
                v-if="col.editable && col.editable.com.name == 'Checkbox'"
                style="text-align: center"
              >
                <v-icon>
                  {{
                    row[col.value]
                      ? 'mdi-checkbox-marked-outline'
                      : 'mdi-crop-square'
                  }}
                </v-icon>
              </div>
              <div
                v-if="
                  col.editable &&
                  col.editable.com == 'Input' &&
                  col.editable.type == 'password'
                "
              >
                * * * * *
              </div>
              <div
                v-else
                :class="col.class"
                :style="{
                  width: col.width,
                }"
              >
                {{ row[col.value] }}
              </div>
            </slot>
          </div>
        </div>
      </div>
    </div>
    <div class="--footer">
      <slot name="footer" :columns="columns"> </slot>
    </div>
    <MobileDet
      v-model:show="showDet"
      :columns="columns"
      :editedData="editedData"
      :row="editedData"
      :idx="selectedIndex"
      :disabled="disabled"
      :editMode="editMode"
      @save="SaveRow"
    >
      <!-- Pass on all named slots -->
      <slot v-for="slot in Object.keys($slots)" :name="slot" :slot="slot" />
      <!-- Pass on all named slots -->
      <template
        v-for="(_, name) in $scopedSlots"
        :slot="name"
        slot-scope="slotData"
        ><slot :name="name" v-bind="slotData"
      /></template>
    </MobileDet>
  </div>
</template>
<script>
import MobileDet from './MobileDet.vue'
export default {
  components: {
    MobileDet,
  },
  data: () => ({
    $table: null,
    $selected: null,
    edited: null,
    $editRow: null,
    editedData: {},

    loading: false,
    lastBind: null,
    loadingMore: false,
    isError: false,
    forceRender: 0,
    selectedIndex: null,
    filters: {},
    showReport: false,
    showPaging: true,
    showColumnSetting: false,
    reportOptions: {},
    allfilters: {},

    showDet: false,

    pageRef: 0,
  }),
  props: {
    id: {
      type: String,
      default: () => {
        if (!window.uuid) window.uuid = 0
        return 'tbl-' + window.uuid++
      },
    },
    datagrid: {
      type: Array,
      default: () => [],
    },
    columns: {
      type: Array,
      default: () => [],
    },
    filter: Function,
    disabled: {
      type: Boolean,
      default: false,
    },
    editMode: {
      type: Array,
      default: () => [],
    },
    dbref: String,
    dbparams: Object,
    height: String,
    width: String,
    groupBy: String,
    onEdit: Function,
    selectedRow: Number,
    autopaging: {
      type: Boolean,
      default: true,
    },
    preHead: {
      type: Boolean,
      default: false,
    },
    doPrint: {
      type: Number,
      default: 0,
    },
    doRebind: {
      type: Number,
      default: 0,
    },
    hasChild: {
      type: Boolean,
      value: false,
    },
  },
  computed: {
    colpos() {
      let topLeft = this.columns.find((c) => c.mobilePos == 'top-left')
      let bottomLeft = this.columns.find((c) => c.mobilePos == 'bottom-left')
      let topRight = this.columns.find((c) => c.mobilePos == 'top-right')
      let bottomRight = this.columns.find((c) => c.mobilePos == 'bottom-right')

      if (!topLeft) {
        topLeft = { ...this.columns[0], mobilePos: 'top-left' }
        if (!bottomLeft && this.columns.length > 1)
          bottomLeft = { ...this.columns[1], mobilePos: 'bottom-left' }
        if (!topRight && this.columns.length > 2)
          topRight = { ...this.columns[2], mobilePos: 'top-right' }
        if (!bottomRight && this.columns.length > 3)
          bottomRight = { ...this.columns[3], mobilePos: 'bottom-right' }
      }

      return [
        topLeft || {},
        bottomLeft || {},
        topRight || {},
        bottomRight || {},
      ]
      // return this.columns
    },
  },
  async mounted() {
    this.pageRef = 0
    this.showPaging = true
    this.dbRebind()
  },
  watch: {
    dbparams() {
      this.pageRef = 0
      this.showPaging = true
      this.dbRebind()
    },
    datagrid() {
      if (this.id && !this.id.match(/tbl-\d+/)) {
        let isShown = localStorage.getItem(`columns-${this.id}`)
        if (isShown) {
          this.columnChanged(JSON.parse(isShown))
        }
      }
    },
    doRebind() {
      this.dbRebind()
    },
  },
  methods: {
    async dbRebind() {
      if (!this.dbref) return
      let sp = this.dbref.replace(/\./, '_Sel')

      this.loading = true
      this.isError = false
      this.$emit('before-bind', this.allfilters)

      this.allfilters = { ...this.dbparams, ...this.filters }
      let allfilters = JSON.stringify(this.allfilters)
      let { data } = await this.$api
        .call(sp, { ...this.dbparams, ...this.filters, PageRef: this.pageRef })
        .catch((err) => {
          console.error(err)
          this.loading = false
          this.isError = true
        })

      if (allfilters != JSON.stringify(this.allfilters)) return

      setTimeout(() => {
        if (!this.$table && document.getElementById(this.id)) {
          this.$table = document.getElementById(this.id)
          let cols = this.$table.querySelectorAll('th')
          cols.forEach((c) => {
            c.width = Math.ceil(c.getBoundingClientRect().width) + 'px'
          })
        }
      }, 500)

      this.$emit('update:datagrid', data)
      this.$emit('after-bind', this.allfilters)
      this.loading = false
    },
    OpenDet(idx, row, evt) {
      if (evt.target && evt.target.closest('.ui-input')) {
        return
      }
      this.selectedIndex = idx
      this.editedData = row
      this.showDet = true
    },
    handleAddNew() {
      this.selectedIndex === null
      if (this.onEdit) {
        this.onEdit()
      } else {
        this.editedData = {}
        this.showDet = true
      }
    },
    removeEdit() {
      this.showDet = false
    },
    async SaveRow() {
      if (!this.dbref) {
        if (
          this.selectedIndex === null ||
          this.selectedIndex === undefined ||
          this.selectedIndex >= this.datagrid.length
        ) {
          this.datagrid.push({})
          this.selectedIndex = this.datagrid.length - 1
        }
        const newData = {
          ...this.dbparams,
          ...this.editedData,
        }
        for (let key in newData) {
          this.datagrid[this.selectedIndex][key] = newData[key]
        }
        this.$emit('save', newData)
        this.removeEdit()
        return
      }
      let sp = this.dbref.replace(/\./, '_Sav')
      let ret = await this.$api.call(sp, {
        ...this.dbparams,
        ...this.editedData,
      })
      if (ret.success) {
        if (
          this.selectedIndex === null ||
          this.selectedIndex === undefined ||
          this.selectedIndex >= this.datagrid.length
        ) {
          this.datagrid.push({})
          this.selectedIndex = this.datagrid.length - 1
        }
        const newData = {
          ...this.editedData,
          ...(ret.data ? ret.data[0] : {}),
        }
        for (let key in newData) {
          this.datagrid[this.selectedIndex][key] = newData[key]
        }
        // console.log(this.selectedIndex, this.datagrid)
        this.removeEdit()
      }
    },
  },
}
</script>
<style lang="scss">
.grid-mobile {
  font-size: 14px;
  width: 100vw;
  max-width: 100%;
  overflow-x: hidden;
  .--header {
    background: #333;
    color: #ddd;
    font-weight: bold;
    padding: 8px 12px;
    display: flex;

    .v-icon {
      color: white !important;
    }
  }
  .--body {
    .--row-nopos {
      display: flex;
      flex-wrap: wrap;
      background: white;
      border-bottom: 1px solid #ddd;
      padding: 12px 8px;
      .no-pos {
        padding: 5px;
      }
    }
    .--row {
      border-bottom: 1px solid #ddd;
      background: white;
      position: relative;
      min-height: 75px;
      .top-left {
        position: absolute;
        top: 10px;
        left: 10px;
        & > .v-btn {
          margin-left: -15px;
        }
      }
      .top-right {
        position: absolute;
        top: 10px;
        right: 10px;
        text-align: right;
      }
      .bottom-left {
        position: absolute;
        bottom: 8px;
        left: 10px;
      }
      .bottom-right {
        position: absolute;
        bottom: 10px;
        right: 10px;
        text-align: right;
      }
      .cellpad {
        margin-top: 6px;
      }
      .ui-input {
        margin-bottom: 0;
      }
    }
  }
}
</style>
