<template>
  <Modal
    title="IMPORT BA PERSETUJUAN"
    v-model:show="xshow"
    width="200px"
    @onSubmit="Save"
    submitText="SIMPAN"
    cancelText="TUTUP"
  >
    <div style="width: 200px">
      <div style="padding: 8px 12px; font-size: 14px"><PERSON><PERSON><PERSON></div>
      <Uploader
        v-model:value="form.BuktiPersetujuan"
        accept=".jpg,.jpeg,.jfif,.png,.heic,.pdf"
      >
      </Uploader>
    </div>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    xshow: false,
    form: {},
  }),
  props: {
    show: Boolean,
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      this.$emit('update:show', val)
    },
  },
  methods: {
    Save() {
      this.$emit('save', this.form.BuktiPersetujuan)
    },
  },
}
</script>
<style lang="scss"></style>
