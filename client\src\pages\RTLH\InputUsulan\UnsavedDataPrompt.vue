<template>
  <Modal
    title="DATA SEBELUMNYA"
    v-model:show="xshow"
    width="450px"
    cancelText=""
    submitText="GUNAKAN DATA"
  >
    <div style="display: flex">
      <div style="padding: 20px 10px 10px 10px">
        <v-icon large>mdi-signal-off</v-icon>
      </div>
      <div style="padding: 10px">
        Ada data yg belum disimpan, <br />
        Apakah anda ingin menampilkannya?
      </div>
    </div>
    <template v-slot:left-action>
      <v-btn variant="text" color="error">ABAIKAN</v-btn>
    </template>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    xshow: false,
  }),
  props: {
    show: Boolean,
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      this.$emit('update:show', val)
    },
  },
  methods: {
    async Save() {
      let ret = await this.$api.call('', {})
      if (ret.success) this.$emit('update:show', false)
    },
  },
}
</script>
