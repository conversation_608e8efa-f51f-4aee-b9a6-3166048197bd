<template>
  <div>
    <div
      id="dvjml"
      class=""
      :style="isMobile ? 'width:100vw' : 'width:calc(100vw - 360px)'"
    >
      <div
        style="
          background: rgba(255, 255, 255, 0.5);
          padding: 15px 13px;
          font-family: Raleway;
          font-weight: bold;
          display: flex;
        "
      >
        <span id="areaname">
          {{ details.Kabupaten }}, {{ details.Kecamatan }},
          {{ details.Kelurahan }}
        </span>
        <span
          style="
            padding: 10px 20px;
            background: white;
            border-radius: 20px;
            margin-left: 20px;
            margin-top: -10px;
          "
          v-show="!isMobile"
        >
          <i class="fa fa-shield" style="color: orangered"></i>
          &nbsp;
          <span id="StatusPBDT">BELUM BEBAS PBDT</span>
        </span>

        <!-- <v-btn
          v-if="!isAddNew"
          style="float:right; margin-top:-5px;"
          color="success"
          v-show="!details.KabKotaApproval && dbparams.Sumber"
          @click="SavePengesahan(1)"
        >
          SETUJUI USULAN
        </v-btn>
        <v-btn
          v-if="!isAddNew"
          style="float:right; margin-top:-5px;"
          color="warning"
          v-show="details.KabKotaApproval && dbparams.Sumber"
          @click="SavePengesahan(0)"
        >
          BATALKAN PERSETUJUAN
        </v-btn> -->
        <v-btn
          small
          text
          style="margin-top: -2px"
          v-if="isMobile"
          @click="showDetail = !showDetail"
        >
          <v-icon>
            {{ showDetail ? 'mdi-chevron-down' : 'mdi-chevron-up' }}
          </v-icon>
        </v-btn>
        <v-spacer />
        <v-btn
          v-if="isAddNew"
          :style="
            isMobile
              ? 'width:100%; margin-top:10px;'
              : 'float:right; margin-top:-5px;'
          "
          color="primary"
          @click="$emit('clickTambahBaru')"
        >
          <v-icon left>mdi-plus</v-icon>
          {{ isMobile ? '' : 'TAMBAH BARU' }}
        </v-btn>
      </div>
      <div
        v-if="!isMobile || showDetail"
        style="
          background: rgba(255, 255, 255, 0.7);
          padding: 5px 15px;
          font-family: Raleway;
          text-transform: uppercase;
          font-size: small;
          display: flex;
          margin-bottom: 10px;
        "
      >
        <span id="JmlRumah" v-if="!isMobile" style="margin-right: 20px">
          JML RUMAH: <b>{{ details.JmlRumah }}</b>
        </span>
        <span id="JmlPenduduk" v-if="!isMobile" style="margin-right: 20px">
          JML PENDUDUK: <b>{{ details.JmlPenduduk }}</b>
        </span>
        <span id="LuasArea" v-if="!isMobile" style="margin-right: 20px">
          LUAS DESA: <b>{{ details.JmlRTLH }}</b>
        </span>
        <span id="JmlBacklog" v-if="!isMobile" style="margin-right: 20px">
          JML BACKLOG: <b>{{ details.JmlBacklog }}</b>
        </span>
        <span id="JmlNonSertifikat" v-if="!isMobile" style="margin-right: 20px">
          BELUM BERSERTIFIKAT: <b>{{ details.JmlNonSertifikat }}</b>
        </span>
        <v-spacer />
        <slot>
          <!-- <v-btn
            text
            color="primary"
            small
            style="margin-top: -5px;"
            v-if="!isMobile"
          >
            <v-icon left>mdi-pencil</v-icon>
            EDIT
          </v-btn> -->
          <v-btn
            style="margin-top: -5px"
            text
            small
            color="primary"
            @click="GeneratePPTX"
          >
            <v-icon left>print</v-icon>
            Profil
          </v-btn>
          <v-btn
            style="margin-top: -5px"
            text
            small
            color="primary"
            @click="PrintVervalBacklog"
          >
            <v-icon left>print</v-icon>
            Form Verval
          </v-btn>
        </slot>
      </div>
    </div>
    <SetujuiPopup v-model:show="showSetujuiPopup" @save="doSavePengesahan" />
  </div>
</template>
<script>
import SetujuiPopup from '../RTLH/InputUsulan/SetujuiPopup.vue'

export default {
  components: {
    SetujuiPopup,
  },
  data: () => ({
    details: {},
    showSetujuiPopup: false,
    showDetail: false,
  }),
  props: {
    dbref: {
      type: String,
      default: 'BLG.SelAreaDet',
    },
    dbparams: [String, Object],
    isAddNew: Boolean,
  },
  computed: {},
  watch: {
    dbparams() {
      this.populate()
    },
  },
  methods: {
    async populate() {
      let { data } = await this.$api.call(this.dbref, this.dbparams)
      if (data.length) this.details = data[0]
    },
    async SavePengesahan(appv) {
      if (this.dbparams.Tahun >= 2022 && appv === 1) {
        this.showSetujuiPopup = true
      } else {
        this.doSavePengesahan(null, appv)
      }
    },
    async doSavePengesahan(bukti, appv) {
      if (this.jmlUsulan < 3 && this.dbparams.tabId == 2 && appv == 1)
        if (!confirm('Jml usulan kurang dari 3, anda yakin?')) return

      await this.$api.call('BLG.SavPengesahanKabupaten', {
        ...this.dbparams,
        IsApproved: bukti ? 1 : appv,
        BuktiPengesahan: bukti,
      })

      let { data } = await this.$api.call(this.dbref, this.dbparams)
      this.details = data[0]
      this.showSetujuiPopup = false
      this.$emit('update:isApproved', this.details.KabKotaApproval)
    },
    async PrintVervalBacklog() {
      window.open(
        this.$api.url + '/report/get/templates/VervalBacklog.pdf',
        '_blank'
      )
    },
    async PrintProfil() {
      let ret = await this.$api.post('/reports/template/ProfilBacklog.doc', {
        ...this.dbparams,
        sp: 'BLG_RptProfilBacklog',
        renderEngine: 'text',
      })
      if (ret.success) this.$api.download('/report/' + ret.data)
    },
    async GeneratePPTX() {
      let ret = await this.$api.post(
        '/reports/template/backlog/ProfilMonev.pptx',
        {
          ...this.dbparams,
          CurrPageUrl: window.location.pathname,
          hasImage: true,
          sp: 'BLG_RptProfilMonev',
        }
      )
      if (ret.success) this.$api.download('/report/' + ret.data)
    },
  },
}
</script>
<style lang="scss"></style>
