<template>
  <Page title="Validasi Data RTLH" :sidebar="true">
    <template v-slot:toolbar>
      <v-icon @click="doPrint++" v-tooltip="'Download Excel'"
        >mdi-microsoft-excel</v-icon
      >
    </template>
    <Sidebar v-model:value="area" />
    <div style="padding: 0 10px 0 10px; width: 100%" v-show="area.Kelurahan">
      <DesaGlance
        :dbparams="area"
        @click-tambah-baru="ClickTambahBaru"
        :addNew="true"
      />
      <Grid
        v-model:datagrid="datagrid"
        dbref="PRM.ValidasiData"
        :dbparams="area"
        :disabled="true"
        :doPrint="doPrint"
        :doRebind="doRebind"
        height="calc(100vh - 243px)"
        @before-bind="BeforeBind"
        @after-bind="DataChanged"
        :columns="[
          {
            name: 'NIK',
            value: 'NIK',
            filter: {
              type: 'search',
              value: 'NIK',
            },
          },
          {
            name: '',
            value: 'IsValidated',
            class: 'plain center',
            width: '40px',
            print: {
              name: 'Lengka<PERSON>',
            },
            filter: {
              type: 'select',
              value: 'IsValidated',
              items: [
                { value: 1, text: 'Sudah' },
                { value: 0, text: 'Belum' },
              ],
            },
          },
          {
            name: 'Nama',
            value: 'KRT_Nama',
            width: '200px',
            class: 'plain fix-width',
            filter: {
              type: 'search',
            },
          },
          {
            name: 'Alamat',
            value: 'Alamat',
            width: '250px',
            class: 'fix-width',
            filter: {
              type: 'search',
            },
          },
          {
            name: 'Kecamatan',
            value: 'Kecamatan',
            hide: true,
          },
          {
            name: 'Kelurahan',
            value: 'Kelurahan',
            hide: true,
          },
          {
            name: 'Prior',
            value: 'RTLH_Prioritas',
            class: 'center',
          },
          {
            name: 'Skor',
            value: 'ScoreTag',
          },
          {
            name: 'DT',
            value: 'NamaData',
            width: '70px',
            filter: {
              type: 'select',
              value: 'TipeData',
              text: 'NamaData',
              dbref: 'PRM.SelTipeData',
              dbparams: {},
            },
          },
          {
            name: 'Intervensi',
            value: 'Intervensi',
            filter: {
              type: 'select',
              value: 'SumberID',
              text: 'SumberName',
              dbref: 'PRM.SelSumber',
              dbparams: { BelumPernah: 1 },
            },
          },
          {
            name: 'Updated',
            value: 'ModifiedDate',
          },
        ]"
      >
        <template #row-NIK="{ row }">
          <nik-block :nik="row.NIK" />
        </template>
        <template #row-KRT_Nama="{ row }">
          <v-btn variant="text" size="small" color="primary" @click="OpenDetail(row.NoUrutRT)">
            {{ row.KRT_Nama }}
          </v-btn>
        </template>
        <template #row-RTLH_Prioritas="{ row }">
          <v-icon
            :color="priorcolors[row.RTLH_Prioritas]"
            v-tooltip="`Prioritas ${row.RTLH_Prioritas}`"
          >
            mdi-numeric-{{ row.RTLH_Prioritas }}-circle
          </v-icon>
        </template>
        <template #row-IsValidated="{ row }">
          <v-icon
            v-if="needSave['bnba-' + row.NoUrutRT + '-' + row.NIK]"
            color="warning"
            v-tooltip="'Ada data yg belum disimpan'"
          >
            mdi-alert
          </v-icon>
          <v-icon
            v-else-if="row.IsValidated"
            color="primary"
            v-tooltip="'Sudah Terverifikasi'"
          >
            mdi-account-check
          </v-icon>
          <v-icon v-else v-tooltip="'Belum Terverifikasi'">
            mdi-account-question-outline
          </v-icon>
        </template>
        <template #row-NamaData="{ row }">
          <span>{{ row.NamaData }}</span>
          <span
            v-if="row.DataTag"
            style="
              font-size: 10px;
              margin-left: 5px;
              background: lightblue;
              padding: 3px;
              border-radius: 3px;
            "
          >
            {{ row.DataTag }}
          </span>
        </template>
        <template #row-ModifiedDate="{ row }">
          <span
            v-show="row.ModifiedDate"
            :class="
              row.UpdatedMonth < 10
                ? 'bold'
                : row.UpdatedMonth > 30
                ? 'gray'
                : ''
            "
            >{{ $filters.format(row.ModifiedDate) }}</span
          >
        </template>
      </Grid>
      <ValidasiDetail
        v-model:show="showDetailModal"
        :noRef="selectedRef"
        :area="area"
        @save="doRebind++"
      />
    </div>
  </Page>
</template>
<script>
import Sidebar from './SideBar.vue'
import DesaGlance from '../DesaGlance.vue'
import ValidasiDetail from '../InputUsulan/ValidasiDetail.vue'

export default {
  components: {
    Sidebar,
    DesaGlance,
    ValidasiDetail,
  },
  data: () => ({
    priorcolors: ['#333', '#e53935', '#F4511E', '#43A047', '#1E88E5'],
    datagrid: [],
    needSave: {},
    area: {},
    showDetailModal: false,
    doRebind: 0,
    doPrint: 0,
    selectedRef: null,
  }),
  methods: {
    ClickTambahBaru() {
      this.showDetailModal = true
      this.selectedRef = 0
    },
    OpenDetail(noRef) {
      this.selectedRef = noRef
      this.showDetailModal = true
    },
    BeforeBind() {},
    DataChanged() {
      this.needSave = {}
      let needsavegrid = []
      Object.keys(localStorage).forEach((key) => {
        if (key.match(/bnba/)) {
          this.needSave[key] = true
          let o = JSON.parse(localStorage.getItem(key))
          if ((o.KodeWilayah || o.KodeDagri) == this.area.KodeDagri)
            needsavegrid.push({
              ...o,
              NoUrutRT: o.NoRef,
              KRT_Nama: o.Nama,
            })
        }
      })
      if (needsavegrid.length)
        this.datagrid = [...needsavegrid, ...this.datagrid]
    },
  },
}
</script>
