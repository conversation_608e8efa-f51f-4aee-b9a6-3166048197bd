var WebSocketServer = require("websocket").server;
var WebSocketClient = require("websocket").client;
var http = require("http");

// WEB SOCKET
module.exports = {
  websocket() {
    var server = http.createServer(function(request, response) {
      // process HTTP request. Since we're writing just WebSockets
      // server we don't have to implement anything.
    });
    server.listen(8289, function() {});

    // create the server
    let wsServer = new WebSocketServer({
      httpServer: server
    });

    // WebSocket server
    wsServer.on("request", function(request) {
      var connection = request.accept(null, request.origin);

      // This is the most important callback for us, we'll handle
      // all messages from users here.
      connection.on("message", function(message) {
        if (message.type === "utf8") {
          // process WebSocket message
        }
      });

      connection.on("close", function(connection) {
        // close user connection
      });
    });
    console.log("WebSocket Running on 8289");

    let wsclient = new WebSocketClient();
    wsclient.on("connect", function(connection) {
      wsclient.send = connection.sendUTF;
      console.log("ws client connected");
    });
    wsclient.on("connectFailed", function(error) {
      console.log("Connect Error: " + error.toString());
    });
    wsclient.connect("ws://localhost:8289/api", "echo-protocol");
  }
};
