version: "3"
services:  
  frontend:
    image: indrawan/perum:client
    ports:
      - 443:443/tcp
      - 80:80/tcp
    volumes:
      - /home/<USER>/webvue/client:/app:rw
      - /etc/letsencrypt:/etc/letsencrypt:rw
      - /home/<USER>/nginx-vue.conf:/etc/nginx/nginx.conf:rw
    depends_on:
      - backend
  backend:
    entrypoint:
      - node
      - app.js
    image: indrawan/perum:server-12
    ports:
      - 8001:8001/tcp
      - 8289:8289/tcp
    restart: always
    volumes:
      - /etc/letsencrypt:/etc/letsencrypt:rw
      - /home/<USER>/webvue/server/tmp:/app/tmp:rw
      - /home/<USER>/webvue/server/common:/app/common:rw
      - /home/<USER>/webapp/uploads:/app/uploads:rw
      - /home/<USER>/webvue/server/api:/app/api:rw
      - /home/<USER>/webvue/server/app.js:/app/app.js:rw
    working_dir: /app
    depends_on:
      - db
  bekend:
    entrypoint:
      - node
      - app.js
    image: indrawan/perum:server
    ports:
      - 8101:8101/tcp
      - 8389:8289/tcp
    restart: always
    volumes:
      - /etc/letsencrypt:/etc/letsencrypt:rw
      - /home/<USER>/temp/kendal/tmp:/app/tmp:rw
      - /home/<USER>/temp/kendal/common:/app/common:rw
      - /home/<USER>/temp/kendal/api:/app/api:rw
      - /home/<USER>/temp/kendal/app.js:/app/app.js:rw
      - /home/<USER>/temp/.well-known:/app/.well-known:rw
    working_dir: /app
    depends_on:
      - db
  db:
    command:
      - mysqld
    entrypoint:
      - docker-entrypoint.sh
    environment:
      - MYSQL_RANDOM_ROOT_PASSWORD=yes
      - affinity:container==307d86a7fd7819d6a0736ef118415a87ed98b6438e52e187789a8d3713301ee5
      - PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
      - GOSU_VERSION=1.12
      - MYSQL_MAJOR=5.7
      - MYSQL_VERSION=5.7.32-1debian10
    image: mysql:5.7
    ports:
      - 3306:3306/tcp
