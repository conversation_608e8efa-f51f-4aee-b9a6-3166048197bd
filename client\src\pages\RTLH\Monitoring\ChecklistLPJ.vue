<template>
  <Modal title="UPLOAD LPJ" v-model:show="xshow" width="1000px" @onSubmit="Save">
    <div class="iblock" style="width: 610px">
      <div style="padding: 5px">
        <div style="display: flex">
          <Uploader
            label="REALISASI PENGGUNAAN DANA"
            :lines="3"
            :required="true"
            v-model:value="forms.LPJ"
            accept=".pdf"
          ></Uploader>
          <Uploader
            label="BUKTI PENGELUARAN UANG"
            :lines="3"
            :required="true"
            v-model:value="forms.Kwitansi"
            accept=".pdf"
          ></Uploader>
          <Uploader
            label="BUKTI SETOR PAJAK"
            :lines="3"
            :required="true"
            v-model:value="forms.BuktiPajak"
            accept=".pdf"
          ></Uploader>
          <Uploader
            label="LPJ PADAT KARYA"
            :lines="3"
            :required="true"
            v-model:value="forms.PadatKarya"
            accept=".pdf"
          ></Uploader>
        </div>
      </div>
    </div>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    xshow: false,
    forms: {},
  }),
  props: {
    show: Boolean,
    nik: [String, Number],
    rowData: Object,
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      if (val) this.Populate()
      else this.forms = {}
      this.$emit('update:show', val)
    },
  },
  methods: {
    async Populate() {
      let d = await this.$api.call('PRM_SelPersonPic', {
        NIK: this.nik,
      })
      if (d.data.length) this.forms = d.data[0]
    },
    async Save() {
      let ret = await this.$api.call('PRM_SavMonevLPJ', {
        NIK: this.nik,
        ...this.forms,
      })
      if (ret.success) this.$emit('update:show', false)
    },
  },
}
</script>
