<template>
  <Modal
    title="PELAKSANAAN REKOM"
    v-model:show="xshow"
    width="1000px"
    @onSubmit="Save"
  >
    <div class="iblock" style="width: 310px">
      <div style="padding: 5px">
        <div
          style="
            padding: 10px 0;
            background: #f3f3f3;
            width: 100%;
            display: flex;
            font-size: small;
          "
        >
          <div class="iblock" style="width: 140px; padding-left: 10px">
            <i class="fa fa-user"></i>LAP. PELAKSANAAN
          </div>
          <div class="iblock" style="flex: 1; padding-left: 10px">
            <i class="fa fa-home"></i>SURAT TANGGUNG JAWAB
          </div>
        </div>
        <div style="display: flex">
          <Uploader v-model:value="forms.LapPelaksanaan" accept=".pdf"></Uploader>
          <Uploader v-model:value="forms.SuratTggJwb" accept=".pdf"></Uploader>
        </div>
      </div>
    </div>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    xshow: false,
    forms: {},
  }),
  props: {
    show: Boolean,
    area: {
      type: Object,
      default: () => ({}),
    },
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      if (val) this.Populate()
      else this.forms = {}
      this.$emit('update:show', val)
    },
  },
  methods: {
    async Populate() {
      let d = await this.$api.call('PRM_SelMonevByRekom', this.area)
      if (d.data.length) this.forms = d.data[0]
    },
    async Save() {
      let ret = await this.$api.call('PRM_SavMonevByRekom', {
        ...this.area,
        ...this.forms,
      })
      if (ret.success) this.$emit('update:show', false)
    },
  },
}
</script>
