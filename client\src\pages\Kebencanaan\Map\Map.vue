<template>
  <div id="page-db-map">
    <Map
      width="100vw"
      height="calc(100vh - 110px)"
      v-model:lat="detail.GeoLat"
      v-model:lon="detail.GeoLng"
      v-model:value="mapValue"
      :searchbox="false"
      :markers="markers"
      @ready="MapReady"
      @change="MapChange"
      @marker-click="MapMarkerClick"
      @marker-hover="MapMarkerHover"
    />
    <div class="detail-panel" v-show="!showDetail">
      <div class="padding">
        <Checkbox
          v-for="(layer, idx) in layers"
          :key="idx"
          :value="layer.visible"
          @click="LayerClick(layer, ...arguments)"
        >
          <v-icon
            :color="layerIcons[layer.name].color"
            style="font-size:small; margin-left:8px"
            >{{ layerIcons[layer.name].icon }}</v-icon
          >
          <div class="--text">{{ layer.name }}</div>
        </Checkbox>
      </div>
    </div>
    <div class="detail-panel" v-show="showDetail">
      <div class="padding">
        <div style="font-size:12px;">
          {{ detail.Tahun }}
        </div>
        <div style="margin-top:-3px">
          {{ detail.Bencana }}
        </div>
        <div style="font-size:12px; color:gray;">
          {{ detail.Kabupaten }}, {{ detail.Kecamatan }}, {{ detail.Kelurahan }}
        </div>
        <br />
        <div style="font-size:12px; color:gray;">
          <div class="iblock" style="color:black; width:150px;">
            Rumah Rusak
          </div>
          {{ detail.RumahRusak }}
        </div>
        <div style="font-size:12px; color:gray;">
          <div class="iblock" style="color:black; width:150px;">
            Fasum Rusak
          </div>
          {{ detail.FasumRusak }}
        </div>
        <div style="font-size:12px; color:gray;">
          {{ detail.Keterangan }}
        </div>
      </div>
      <div
        style="display: flex;
          width: 310px;
          max-height: 300px;
          flex-wrap: wrap;
          overflow: auto;"
      >
        <div
          style="width:150px;"
          v-for="(item, idx) in photos"
          :key="idx"
          v-show="detail[item.key]"
        >
          <div style="padding:5px 8px; font-size:12px; background:#f3f3f3;">
            {{ item.text }}
          </div>
          <div
            class="imgbox"
            :style="{
              'background-image': `url(${detail[item.key]})`,
            }"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data: () => ({
    markers: [],
    layers: [],
    detail: {},
    showDetail: false,
    xbdparams: {},
    mapValue: null,
    viewType: '',
    layerIcons: {
      Banjir: { icon: 'mdi-waves', color: 'rgba(64, 164, 223,0.8)' },
      'Gunung Berapi': { icon: 'mdi-triangle', color: 'rgba(255,0,0,0.8)' },
      'Gempa Bumi': { icon: 'mdi-square', color: 'rgba(255,42,42,0.8)' },
      'Tanah Longsor': { icon: 'mdi-square', color: 'rgba(165,42,42,0.8)' },
      'Gelombang Pasang': {
        icon: 'mdi-triangle',
        color: 'rgba(64, 164, 223,0.8)',
      },
      'Puting Beliung': {
        icon: 'mdi-weather-windy',
        color: 'rgba(64, 164, 223,0.8)',
      },
    },
    photos: [
      { key: 'Profile', text: 'Profile' },
      { key: 'RumahDepan', text: 'RUMAH DEPAN' },
      { key: 'RumahSamping', text: 'RUMAH SAMPING' },
      { key: 'Atap0', text: 'ATAP 0%' },
      { key: 'Atap50', text: 'ATAP 50%' },
      { key: 'Atap100', text: 'ATAP 100%' },
      { key: 'Lantai0', text: 'LANTAI 0%' },
      { key: 'Lantai50', text: 'LANTAI 50%' },
      { key: 'Lantai100', text: 'LANTAI 100%' },
      { key: 'Dinding0', text: 'DINDING 0%' },
      { key: 'Dinding50', text: 'DINDING 50%' },
      { key: 'Dinding100', text: 'DINDING 100%' },
    ],
  }),
  props: {
    dbref: String,
    dbparams: Object,
    noRef: Number,
    value: String,
  },
  watch: {
    noRef(val) {
      this.Populate(val)
    },
    'detail.GeoLat'(val) {
      if (this.detail.GeoLng)
        this.$emit('update:value', val + '|' + this.detail.GeoLng)
    },
    'detail.GeoLng'(val) {
      if (this.detail.GeoLat)
        this.$emit('update:value', this.detail.GeoLat + '|' + val)
    },
  },
  methods: {
    async MapReady() {
      let ret = await this.$api.call(this.dbref, this.xbdparams)
      this.markers = ret.data
    },
    MapChange(map) {
      this.layers = []
      map.getLayers().forEach((layer) => {
        if (layer.values_.name) {
          this.layers.push({
            instance: layer,
            name: layer.values_.name.replace('marker-', ''),
            visible: layer.values_.visible,
          })
        }
      })
    },
    async MapMarkerClick(data) {
      let d = JSON.parse(data)
      this.$emit('marker-click', d)
    },
    MapMarkerHover(data) {
      if (data) {
        let d = JSON.parse(data)
        if (d) {
          this.detail = d
          this.showDetail = true
        }
      } else {
        this.showDetail = false
      }
    },
    LayerClick(layer, visible) {
      layer.visible = visible
      layer.instance.setVisible(visible)
    },
    async Populate(val) {
      let ret = await this.$api.call('PRM.SelPersonSummary', { NoUrutRT: val })
      if (ret.data[0]) {
        this.detail = ret.data[0]
        this.showDetail = true
      }
    },
  },
}
</script>
<style lang="scss">
#page-db-map {
  position: relative;
  .detail-panel {
    width: 310px;
    background: white;
    position: absolute;
    right: 10px;
    top: 10px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.7);
    border-radius: 5px;
    overflow: hidden;
    max-height: 600px;
  }
  .imgbox {
    background: #ddd;
    width: 150px;
    height: 150px;
  }
}
</style>
