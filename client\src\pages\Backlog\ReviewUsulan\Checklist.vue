<template>
  <Modal
    title="CHECKLIST PROPOSAL"
    v-model:show="xshow"
    width="500px"
    :saving="saving"
    @onSubmit="Save"
  >
    <div style="display: flex" class="form-inline">
      <div class="iblock" style="width: 400px">
        <div style="padding: 5px">
          <Checkbox
            text="Surat Permohonan"
            v-model:value="forms.CekSuratPermohonan"
          />
          <Checkbox
            text="Surat Keputusan Pokmas"
            v-model:value="forms.CekKeputusanPokmas"
          />
          <Checkbox text="Berita Acara" v-model:value="forms.CekBeritaAcara" />
          <Checkbox text="Daftar Hadir" v-model:value="forms.CekDaftarHadir" />
          <Checkbox text="RAB" v-model:value="forms.CekRAB" />
          <Checkbox text="Mampu Swadaya" v-model:value="forms.CekMampuSwadaya" />
        </div>
      </div>
    </div>
    <template v-slot:left-action>
      <div style="padding-left: 17px; color: gray">
        <Checkbox text="Check Semua" v-model:value="checkAll" />
      </div>
    </template>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    xshow: false,
    saving: false,
    forms: {
      CekSuratPermohonan: 0,
      CekKeputusanPokmas: 0,
      CekBeritaAcara: 0,
      CekDaftarHadir: 0,
      CekRAB: 0,
      CekMampuSwadaya: 0,
    },
    checkAll: false,
  }),
  props: {
    show: Boolean,
    nik: [String, Number],
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      this.$emit('update:show', val)
    },
    nik(val) {
      if (val && this.xshow) this.populate()
    },
    checkAll(val) {
      for (let x in this.forms) {
        if (x.match(/^Cek/)) this.forms[x] = val
      }
    },
  },
  methods: {
    async populate() {
      let { data } = await this.$api.call('BLG.SelProposalCheck', {
        NIK: this.nik,
      })
      this.forms = data[0]
    },
    async Save() {
      this.saving = true
      let ret = await this.$api.call('BLG.SavProposalCheck', {
        ...this.forms,
        NIK: this.nik,
      })
      this.saving = false
      if (ret.success) this.$emit('update:show', false)
    },
  },
}
</script>
<style lang="scss">
.dv-kerusakan .checked {
  color: red;
}
</style>
