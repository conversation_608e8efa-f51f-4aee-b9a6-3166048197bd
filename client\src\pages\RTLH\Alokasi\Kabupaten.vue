<template>
  <div>
    <div style="display: flex">
      <XSelect
        v-model:value="proposal"
        dbref="PRM.SelProposal"
        :value-as-object="true"
        width="95px"
        style="margin-right: 10px"
      />
      <Checkbox
        v-model:value="hasAllocation"
        style="margin-left: 10px; margin-top: 2px"
        text="Mempunyai Alokasi"
      />
    </div>
    <Grid
      v-model:datagrid="forms.XmlAlokasi"
      dbref="PRM.AlokasiKab"
      :dbparams="params"
      :filter="filterGrid"
      :disabled="true"
      :autopaging="false"
      height="calc(100vh - 240px)"
      :columns="[
        {
          name: 'Kabupaten',
          value: 'Kabupaten',
          width: '150px',
        },
        {
          name: '<PERSON><PERSON>',
          value: 'Kuota',
          class: 'plain',
        },
        {
          name: 'Terpakai',
          value: 'Terpakai',
          class: 'plain',
        },
        {
          name: '',
          value: '<PERSON>a',
          class: 'plain',
        },
      ]"
    >
      <template #row-Kuota="{ row }">
        <XInput v-model:value="row.Kuota" type="number" width="95px" />
      </template>
      <template #row-Terpakai="{ row }">
        <div
          style="text-align: right; padding-right: 8px"
          :style="{
            color:
              row.Terpakai > row.Kuota
                ? 'red'
                : row.Terpakai == row.Kuota
                ? 'blue'
                : 'black',
          }"
        >
          {{ row.Terpakai }}
        </div>
      </template>
      <template #row-Desa="{ row }">
        <v-btn
          size="x-small"
          variant="text"
          color="primary"
          @click="OpenDesa(row)"
        >
          DESA
          <template #append>
            <v-icon size="x-small">mdi-open-in-new</v-icon>
          </template>
        </v-btn>
      </template>
    </Grid>
    <br />
    <v-btn color="primary" @click="Save"> SIMPAN </v-btn>
    <ModalDesa v-model:show="showDesa" :params="selected" />
  </div>
</template>
<script>
import ModalDesa from './ModalDesa.vue'
export default {
  components: { ModalDesa },
  data: () => ({
    proposal: {
      InputName: new Date().getFullYear(),
    },
    forms: {
      XmlAlokasi: [],
    },
    hasAllocation: false,
    showDesa: false,
    selected: {},
  }),
  computed: {
    params() {
      let { InputName } = this.proposal
      return { Tahun: InputName }
    },
  },
  methods: {
    async Save() {
      await this.$api.call('PRM_SavAlokasiKab', {
        ...this.forms,
        Tahun: this.proposal.InputName,
      })
    },
    filterGrid(row) {
      return this.hasAllocation ? Boolean(row.Kuota) : true
    },
    OpenDesa(row) {
      this.selected = { ...row, Tahun: this.proposal.InputName, Sumber: 2 }
      this.showDesa = true
    },
  },
}
</script>
<style lang="scss"></style>
