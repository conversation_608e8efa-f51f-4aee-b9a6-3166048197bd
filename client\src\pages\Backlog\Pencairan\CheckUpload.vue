<template>
  <Uploader
    v-model:value="dataValue"
    class="upload-check"
    :accept="accept"
    style="width: 100%"
    @start="isLoading = true"
    @change="handleChange"
  >
    <template #default="{ opener, dropper, dragOver, dragLeave }">
      <v-btn
        @click="opener"
        @drop.prevent="dropper"
        @dragover="dragOver"
        @dragenter="dragOver"
        @dragleave="dragLeave"
        text
        style="text-align: left"
      >
        <v-icon v-if="isLoading" left> mdi-loading mdi-spin </v-icon>
        <v-icon v-else-if="hasValue" left> mdi-checkbox-marked-outline </v-icon>
        <v-icon v-else left> mdi-crop-square </v-icon>
        {{ label }}
      </v-btn>
      <v-spacer />
      <v-icon
        small
        v-tooltip="'view'"
        v-show="hasValue"
        color="success"
        style="width: 36px"
        @click="OpenFile"
      >
        mdi-eye
      </v-icon>
    </template>
  </Uploader>
</template>
<script>
export default {
  data: () => ({
    isLoading: false,
    hasValue: false,
  }),
  props: {
    value: String,
    label: String,
    accept: String,
  },
  watch: {
    value(val) {
      if (!val) this.hasValue = false
      else this.hasValue = true
    },
  },
  computed: {
    dataValue: {
      get() {
        // console.log(this.value)
        return this.value
      },
      set(val) {
        console.log('dataValue:', val)
        if (val) this.hasValue = true
        else this.hasValue = false
        this.$emit('update:value', val)
      },
    },
  },
  mounted() {
    if (!this.value) this.$emit('update:value', null)
  },
  methods: {
    OpenFile() {
      window.open(
        this.$api.url + this.value.replace(/^tmp\//, '/reports/get/'),
        '_blank'
      )
    },
    handleChange() {
      this.isLoading = false
    },
  },
}
</script>
<style lang="scss">
.upload-check {
  width: auto;
  display: flex;
  .v-btn__content {
    text-transform: capitalize;
    font-family: Montserrat;
    font-weight: normal;
    font-size: 14px;
    letter-spacing: 0;
  }
}
</style>
