<template>
  <Page title="Data Dinas">
    <div class="form-inline padding" style="background:white;">
      <XInput label="Nama Dinas" v-model:value="forms.CompanyName" width="300px" />
      <XInput label="Deskripsi" v-model:value="forms.Description" width="300px" />
      <TextArea label="Alamat" v-model:value="forms.Address" width="300px" />
      <XInput label="Telp." v-model:value="forms.Phone" width="300px" />
      <XInput label="Fax." v-model:value="forms.Fax" width="300px" />
      <XInput label="Email" v-model:value="forms.Email" width="300px" />
      <br />
      <v-btn @click="Save" color="primary">SIMPAN</v-btn>
    </div>
  </Page>
</template>
<script>
export default {
  data: () => ({
    forms: {},
  }),
  async mounted() {
    this.forms = await this.$api.getOne('Arch.SelCompany')
  },
  methods: {
    Save() {
      this.$api.call('Arch.SavCompany', this.forms)
    },
  },
}
</script>
