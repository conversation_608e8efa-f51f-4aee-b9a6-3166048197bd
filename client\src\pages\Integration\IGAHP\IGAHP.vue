<template>
  <Page :title="isMobile ? 'IGAHP' : 'IGAHP - Lentera Hijau'" :sidebar="true">
    <template #toolbar>
      <v-btn
        v-if="!igahpUser?.username"
        small
        color="success"
        style="margin-right: 10px"
        @click="showLoginModal = true"
      >
        {{ isMobile ? 'LOGIN' : 'LOGIN IGAHP' }}
      </v-btn>
      <v-btn
        v-else-if="!isMobile"
        small
        variant="outlined"
        color="success"
        style="margin-right: 10px"
        @click="showLoginModal = true"
      >
        IGAHP: {{ igahpUser?.username }}
      </v-btn>
      <v-btn
        v-if="igahpUser.username"
        v-tooltip="{ content: tooltipStatus, trigger: 'hover click' }"
        small
        variant="outlined"
        color="success"
      >
        {{ status.Percentage || 0 }}%
      </v-btn>
      <MenuButton :menu="['BNBA Desa', 'Data Saya']" @item-click="DownloadData">
        <template #default="{ on }">
          <v-icon v-on="on">
            {{
              downloadLoading ? 'mdi-loading mdi-spin' : 'mdi-microsoft-excel'
            }}
          </v-icon>
        </template>
      </MenuButton>
    </template>
    <SideBar v-model:value="area" />
    <div
      v-show="area.Kelurahan"
      style="padding: 0 10px; width: 100%; overflow-y: hidden"
    >
      <div
        style="
          background: rgba(255, 255, 255, 0.5);
          padding: 13px 15px 10px 15px;
          font-family: Raleway;
          font-weight: bold;
          display: flex;
        "
      >
        <span
          id="areaname"
          style="
            padding-top: 3px;
            max-width: calc(80% - 50px);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          "
        >
          {{ area.Kabupaten }}, {{ area.Kecamatan }}, {{ area.Kelurahan }}
        </span>
        <v-spacer />
        <v-btn
          :style="isMobile ? 'width:40px; ' : 'float:right;'"
          style="margin-top: -5px"
          color="primary"
          @click="ClickTambahBaru"
        >
          <v-icon :left="!isMobile">mdi-plus</v-icon>
          {{ isMobile ? '' : 'TAMBAH BARU' }}
        </v-btn>
      </div>
      <div style="margin-left: 5px; display: flex">
        <XInput
          v-model:value="keyword"
          type="text"
          placeholder="Cari Nama / NIK.."
          style="width: calc(100% - 100px)"
          width="100%"
          :left-icon="keyword ? 'mdi-close' : 'mdi-magnify'"
          @left-icon-click="
            keyword = ''
            populate(0)
          "
          @change="populate(0)"
          @click="searchMode = true"
          @blur="searchMode = false"
        />
        <XSelect
          v-show="!searchMode"
          v-model:value="currStatus"
          :items="opsiStatus"
          @change="populate(0)"
        />
        <v-btn
          small
          text
          color="primary"
          style="margin-top: 1px"
          @click="PrintKuisonerKosong"
        >
          <v-icon left>mdi-printer</v-icon>
          KUISIONER
        </v-btn>
      </div>
      <Grid
        :key="gridKey"
        v-model:datagrid="datagrid"
        :disabled="true"
        height="calc(100vh - 200px)"
        :columns="columns"
        :loading="loading"
        @load-more="populate"
      >
        <template #row-nik="{ row }">
          <v-btn
            v-if="row.namaLengkap"
            text
            small
            color="primary"
            @click="OpenDetail(row)"
          >
            {{ row.nik || '&lt; kosong &gt;' }}
          </v-btn>
        </template>
        <template #row-alamatDomisili="{ row }">
          {{ row.alamatDomisili || row.alamatKtp }}
        </template>
        <template #row-lentera="{ row }">
          <v-icon
            v-if="needSave['igahp-' + row.nik]"
            v-tooltip="'Ada data yg belum disimpan'"
            color="warning"
          >
            mdi-alert
          </v-icon>
          <img
            v-else
            v-tooltip="'Data Lentera Hijau'"
            src="/imgs/igahp.svg"
            height="20px"
            style="margin-top: 3px; opacity: 0.7"
            :style="{
              filter: row.status === '2d' ? 'none' : 'grayscale(100%)',
              opacity: row.status === 3 ? '0.8' : '0.5',
              display: ['2a', '2d', 3, 4, 5].includes(row.status)
                ? 'inline-block'
                : 'none',
            }"
          />
        </template>
        <template #row-status="{ row }">
          <v-btn
            v-if="row.status == 'x'"
            small
            text
            variant="outlined"
            disabled
          >
            BELUM DISIMPAN
          </v-btn>
          <v-btn
            v-if="row.status == 'not_sent'"
            small
            color="secondary"
            @click="sync2(row)"
          >
            KIRIM KE IGAHP
          </v-btn>
          <MenuButton
            v-else-if="row.status"
            :menu="GetMenu(row.status)"
            :disabled="
              row.status == 1 || status === undefined || row.statusDisabled
            "
            @item-click="ChangeStatus($event, row)"
          >
            <template #default="{ on }">
              <v-btn
                small
                :color="GetColor(row.status)"
                :outlined="row.status == 0"
                v-on="on"
              >
                {{ statuses[row.status] || 'TDK LOLOS' }}
              </v-btn>
            </template>
          </MenuButton>
        </template>
        <template #footer="{ columns }">
          <!-- <td :colspan="columns.length" v-show="loading">
            <div style="text-align: center; padding: 12px">
              <v-icon>mdi-loading mdi-spin</v-icon>
              Loading
            </div>
          </td> -->
          <td v-show="!loading && isError" :colspan="columns.length">
            <div style="text-align: center; padding: 12px">
              Error while loading data
              <v-btn color="primary" size="small" variant="text" @click="populate(0)">
                COBA LAGI
              </v-btn>
            </div>
          </td>
        </template>
      </Grid>
    </div>
    <ValidasiDetail
      v-model:show="showDetailModal"
      :nik="selectedNIK"
      :area="area"
      :igahp="true"
      :igahp-data="igahpData"
      :disabled="user.RolePositionID == 3 || disableSaving"
      @save="doRebind++"
      @error="handleError"
    />
    <!-- <ValidasiDetail
      v-model:show="showDetailModal"
      :nik="selectedNIK"
      :area="area"
      :igahp="true"
    /> -->
    <Login
      v-model:show="showLoginModal"
      @close="showLoginModal = false"
      @submit="Login"
    />
  </Page>
</template>
<script>
import { mapGetters, mapActions } from 'vuex'
import SideBar from './SideBar.vue'
import Login from './Login.vue'
import ValidasiDetail from './ValidasiDetail.vue'

export default {
  components: { SideBar, ValidasiDetail, Login },
  data: () => ({
    area: {},
    datagrid: [],
    needSave: {},
    selectedNIK: '',
    igahpData: null,
    showDetailModal: false,
    showLoginModal: false,
    searchMode: false,
    doRebind: 1,
    gridKey: 1,
    keyword: '',
    downloadLoading: false,
    loading: false,
    status: {},
    currStatus: '2a',
    tooltipStatus: '',
    disableDetail: false,
    isError: false,
    checkInterval: null,
    disableSaving: false,
    // igahpUser: {
    //   username: '',
    //   password: '',
    // },
    columns: [
      {
        name: 'NIK',
        value: 'nik',
        class: 'plain',
      },
      {
        name: 'Nama',
        value: 'namaLengkap',
        width: '180px',
      },
      {
        name: 'Alamat',
        value: 'alamatDomisili',
        width: '250px',
      },
      // {
      //   name: 'Skor',
      //   value: 'scoreTag',
      // },
      // {
      //   name: 'Intervensi',
      //   value: 'sumberName',
      // },
      // {
      //   name: 'DT',
      //   value: 'namaData',
      // },
      {
        name: 'Surveyor',
        value: 'namaSurveyor',
      },
      {
        name: '',
        value: 'lentera',
        class: 'plain',
      },
      {
        name: 'Status',
        value: 'status',
        class: 'plain',
      },
    ],
    statuses: {
      0: 'Data Awal',
      1: 'Checking',
      '2a': 'Siap Survey',
      '2d': 'Siap Survey',
      '2b': 'Tdk Lolos',
      3: 'Submitted',
      4: 'Revisi',
      5: 'Disetujui',
      x: 'Belum Disimpan',
    },
  }),
  computed: {
    ...mapGetters({
      user: 'getUser',
      igahpUser: 'getIgahpUser',
    }),
    opsiStatus() {
      return Object.keys(this.statuses)
        .filter((k) => k != '2d')
        .map((k) => {
          return { val: k, txt: this.statuses[k] }
        })
    },
  },
  watch: {
    doRebind() {
      this.populate(0)
    },
    area() {
      this.populate()
    },
  },
  mounted() {
    // if (sessionStorage.getItem('igahpUser')) {
    //   this.igahpUser = JSON.parse(sessionStorage.getItem('igahpUser'))
    // }
    if (this.user.RolePositionID === 3) {
      this.currStatus = '3'
    }
    this.GetStatus()
  },
  methods: {
    ...mapActions(['setIgahpUser']),
    async populate(page = 0) {
      let needsavegrid = []

      if (page === 0) {
        this.gridKey++
        this.loading = true
        this.datagrid = []
        this.needSave = {}
        Object.keys(localStorage).forEach((key) => {
          if (key.match(/igahp/)) {
            this.needSave[key] = true
            let o = JSON.parse(localStorage.getItem(key))
            if (o.KodeWilayah == this.area.KodeDagri && this.currStatus == '2a')
              needsavegrid.push({
                nik: o.NIK,
                namaLengkap: o.Nama,
                alamatDomisili: o.Alamat,
                status: 'x',
              })
          }
        })
      }
      const d = await this.$api.get(
        // `/api/igahp/quesioner/allQuesioner-hasilSurvei?page=0&size=25&wilayah=${this.area.KodeDagri}&search=`
        `/api/igahp/list?page=${page}&size=100&wilayah=${this.area.KodeDagri}&search=${this.keyword}&status=${this.currStatus}`
      )
      // const d = await this.$api.call('IGA.SelAuto', { nocache: true })
      // clearInterval(this.checkInterval)
      // if (this.keyword) {
      //   this.checkInterval = setTimeout(this.populate, 1000 * 60 * 1.5)
      // }
      this.isError = !d.success
      if (page === 0) {
        if (needsavegrid.length) needsavegrid.push({})
        this.datagrid = [...needsavegrid, ...d.data.content]
        this.loading = false
      } else this.datagrid = this.datagrid.concat(d.data.content)
    },
    async DownloadData(txt) {
      if (txt === 'BNBA Desa') {
        this.BNBADesa()
      } else if (txt === 'Data Saya') {
        this.DataSaya()
      }
    },
    async BNBADesa() {
      if (!this.area.KodeDagri || this.area.KodeDagri == 'undefined') {
        alert('Silahkan pilih wilayah terlebih dahulu')
        return
      }
      this.downloadLoading = true
      // Create a Blob with the CSV data and type
      const d = await this.$api.get(
        // `/api/igahp/quesioner/allQuesioner-hasilSurvei?page=0&size=25&wilayah=${this.area.KodeDagri}&search=`
        `/api/igahp/list?page=0&size=1000&wilayah=${this.area.KodeDagri}&search=`
      )
      if (!d.data.content?.length) {
        this.downloadLoading = false
        return
      }

      let data = this.columns.map((col) => col.name).join(',') + '\n'
      d.data.content.forEach((row) => {
        data +=
          '"\'' +
          row.nik +
          '","' +
          row.namaLengkap +
          '",' +
          (row.alamatDomisili || '') +
          ',"' +
          (row.scoreTag || '') +
          '",' +
          (row.sumberName || '') +
          ',' +
          (row.namaData || '') +
          ',' +
          // (row.lentera ? 'Ya' : 'Tidak') +
          '' +
          ',' +
          this.statuses[row.status] +
          '\n'
      })
      this.ExportCSV(data)
    },
    async DataSaya() {
      this.downloadLoading = true
      // Create a Blob with the CSV data and type
      const d = await this.$api.call('IGA.SelSurveyorData', { nocache: true })
      if (!d.data?.length) {
        this.downloadLoading = false
        return
      }

      let data = `NIK,Nama,Kecamatan,Desa,Status\n`
      d.data.forEach((row) => {
        data += `'${row.NIK},${row.Nama},${row.Kecamatan},${row.Kelurahan},${row.Status}\n`
      })
      this.ExportCSV(data)
    },
    ExportCSV(data) {
      const blob = new Blob([data], { type: 'text/csv' })

      // Create a URL for the Blob
      const url = URL.createObjectURL(blob)

      this.downloadLoading = false
      // Create an anchor tag for downloading
      const a = document.createElement('a')

      // Set the URL and download attribute of the anchor tag
      a.href = url
      a.download = 'download.csv'

      // Trigger the download by clicking the anchor tag
      a.click()
    },
    GetMenu(status) {
      if (status == '0') {
        return ['Check Subsidi']
      } else if (
        ['2a', '2d', '4'].includes(status) &&
        [1, 26].includes(this.user.RolePositionID)
      ) {
        return ['Submit']
      } else if (
        ['4'].includes(status) &&
        [1, 2, 3].includes(this.user.RolePositionID)
      ) {
        return ['Setujui', 'Revisi']
      } else if (
        status == '2b' &&
        [1, 2, 26].includes(this.user.RolePositionID)
      ) {
        // return ['Check Ulang']
        return []
      } else if (status == '4' && [26].includes(this.user.RolePositionID)) {
        return ['Submit']
      } else if (
        ['3', '5'].includes(status) &&
        [1, 2, 3].includes(this.user.RolePositionID)
      ) {
        return ['Setujui', 'Revisi']
      }

      return []
    },
    GetColor(status) {
      if (status === 0 || status == 1) {
        return '#aaa'
      } else if (status == '2b') {
        return 'error'
      } else if (status == 3) {
        return 'info'
      } else if (status == 4) {
        return 'error'
      } else if (status == 5) {
        return 'success'
      } else if (!status) {
        return 'error'
      }

      return ''
    },
    async GetStatus() {
      let s = await this.$api.call('IGA.SelSurveyorStatus', { nocache: true })
      this.status = s.data[0] || {}
      this.tooltipStatus = `Ada Minat: ${this.status.AdaMinat}<br/>
        Lengkap: ${this.status.Lengkap}<br/>
        Submitted: ${this.status.Submitted}<br/>
        Disetujui: ${this.status.Approved}<br/>
        Revisi: ${this.status.Rejected}`
    },
    async PrintKuisonerKosong() {
      this.$api.download('/report/get/templates/Kuisioner2024.docx')
    },
    CheckVerification(row) {
      return row.layakKonstruksi
    },
    OpenDetail(row) {
      this.disableSaving = false
      if (
        this.user.RolePositionID == 26 &&
        row.usernameEdit &&
        row.usernameEdit != this.igahpUser.username
      ) {
        this.$api.notify('Data sedang diedit oleh user lain', 'error')
        this.disableSaving = true
      }
      this.selectedNIK = row.nik
      this.disableDetail = !['0', '1', '2a', '3', '4'].includes(row.status)
      this.igahpData = {
        NIK: row.nik,
        Nama: row.namaLengkap,
        Alamat: row.alamatDomisili,
      }
      this.showDetailModal = true
    },
    ClickTambahBaru() {
      this.disableSaving = false
      this.showDetailModal = true
      this.selectedNIK = 0
    },
    Login(opts) {
      // sessionStorage.setItem('igahpUser', JSON.stringify(opts))
      // this.igahpUser = opts
      this.setIgahpUser(opts)
    },
    async sync2(row) {
      this.loading = true
      await this.$api.post(this.$api.url + '/api/igahp/sync2', {
        NIK: [row.nik],
        ...this.area,
        ...this.igahpUser,
      })
      this.populate()
    },
    async ChangeStatus(txt, row) {
      row.statusDisabled = true
      if (!this.igahpUser.username && txt != 'Check Subsidi') {
        this.showLoginModal = true
        return
      }
      if (txt == 'Check Subsidi') {
        this.$api.post(this.$api.url + '/api/igahp/check', {
          nik: row.nik,
          nama: row.namaLengkap,
        })
        row.status = 1
        return
      } else if (txt == 'Submit') {
        let d = await this.$api.call('IGA.SelCheckDataLengkap', {
          NIK: row.nik,
        })
        if (d.data.length && !d.data[0].Lengkap) {
          this.$api.notify(
            d.data[0].Keterangan || 'Data belum lengkap',
            'error'
          )
          return
        }
      }
      let status = 0
      if (txt === 'Submit') {
        status = 3
        await this.$api.post(this.$api.url + '/api/igahp/sync', {
          ...this.igahpUser,
          NIK: row.nik,
          sync: true,
        })
      } else if (txt === 'Setujui') {
        status = 5
      } else if (txt === 'Revisi') {
        status = 4
      }
      let d = await this.$api.req({
        method: 'PUT',
        url:
          this.$api.url +
          `/api/igahp/quesioner/updateStatus/${row.id}/${status}`,
        data: {
          ...this.igahpUser,
          NIK: row.nik,
          KodeDagri: row.kodewilayah,
        },
      })
      if (d.success) {
        this.populate()
      } else {
        this.$api.notify(d.message, 'error')
        this.showLoginModal = true
      }
      delete row.statusDisabled
    },
    handleError(err) {
      if (err.match(/Login IGAHP/i)) {
        this.showLoginModal = true
        this.setIgahpUser({})
      }
    },
  },
}
</script>
<style lang="scss">
.page-igahp---lentera-hijau {
  .page-content {
    margin-top: -5px;
  }
}
</style>
