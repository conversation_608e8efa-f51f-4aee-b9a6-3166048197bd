<template>
  <v-container style="max-width: 100vw; padding: 0">
    <div class="login-box">
      <div
        style="
          height: 130px;
          background: url('/imgs/appex/banner.png');
          background-size: cover;
          background-position: center;
        "
      ></div>
      <div style="padding: 30px">
        <XInput
          label="No Handphone (Username)"
          v-model:value="forms.Username"
          class="inline"
          width="100%"
        />
        <div
          style="
            text-align: left;
            font-size: small;
            color: #888;
            margin-bottom: 10px;
          "
        >
          nomor ini akan digunakan sebagai user untuk masuk kedalam sistem
        </div>
        <XInput
          label="Password"
          v-model:value="forms.Password"
          type="password"
          width="100%"
        />

        <XInput
          label="Nama Lengkap"
          v-model:value="forms.FullName"
          width="100%"
        />
        <XInput label="NIM" v-model:value="forms.NIM" width="100%" />
        <XInput
          label="Universitas"
          v-model:value="forms.University"
          width="100%"
        />
        <XInput label="Kelompok" v-model:value="forms.Team" width="100%" />
        <XInput label="OTP" v-model:value="forms.OTP" width="100%" />
        <v-btn
          text
          small
          color="primary"
          @click="sendOTP"
          style="width: calc(100% - 40px)"
        >
          KIRIM OTP
        </v-btn>
        <br />
        <br />
        <v-btn
          color="primary"
          @click="submit"
          style="width: calc(100% - 40px)"
          :loading="loading"
        >
          DAFTAR
        </v-btn>
      </div>
    </div>
  </v-container>
</template>

<script>
import { mapActions } from 'vuex'
export default {
  components: {},
  data: () => ({
    forms: {
      username: '',
      password: '',
    },
    loading: false,
    showNews: false,
  }),
  mounted() {
    localStorage.clear()
    sessionStorage.clear()
    this.setMenu(null)
  },
  methods: {
    ...mapActions(['setMenu', 'setUser', 'setIgahpUser']),
    getListMenu(menu) {
      let mlist = {}
      menu.forEach((m) => {
        if (m.child && m.child.length) {
          if (m.MenuUrl) {
            mlist[m.MenuUrl] = this.getListMenu(m.child)
          } else mlist = Object.assign(mlist, this.getListMenu(m.child))
        } else if (m.MenuUrl) mlist[m.MenuUrl] = m.RWX
      })
      return mlist
    },
    async sendOTP() {
      let d = await this.$api.post(
        '/api/send-otp',
        {
          _Phone: this.forms.Username,
          _Purpose: 'registration',
        },
        { notify: true }
      )
    },
    async submit() {
      this.loading = true
      let d = await this.$api.call('EVO.SavUserKKN', this.forms)
      this.loading = false
      if (d.success) {
        this.$router.push('/login')
      }
    },
  },
}
</script>
<style lang="scss">
// .theme--light.v-application {
//   background: #f3f3f3;
// }
.login-box {
  width: calc(100% - 40px);
  max-width: 400px;
  background: white;
  box-sizing: content-box;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  margin: auto;
  margin-top: 10%;
  text-align: center;
  overflow: hidden;
}
</style>
