<template>
  <div class="form-coms ui-datepicker">
    <div v-if="label" class="form-label" :class="{ '--required': hasError }">
      <slot name="label">
        {{ label }}
      </slot>
    </div>
    <div
      class="--pc-input"
      :style="{
        width: width,
      }"
    >
      <DatePicker
        v-model="formattedValue"
        size="small"
        show-icon
        fluid
        icon-display="input"
        date-format="dd M yy"
      />
    </div>
  </div>
</template>
<script>
import DatePicker from 'primevue/datepicker'
import moment from 'moment'

export default {
  components: {
    DatePicker,
  },
  props: {
    label: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    value: {
      type: [String, Date],
      default: null,
    },
    useDefault: Boolean,
    disabled: Boolean,
  },
  emits: ['update:value', 'change'],
  data: () => ({
    showPopup: false,
    vmodel: null,
  }),
  computed: {
    formattedValue: {
      get() {
        if (this.vmodel) return moment(this.vmodel).format('DD-MMM-YYYY')
        else return ''
      },
      set(val) {
        if (val.match(/\d?\d-\w{3}-\d{4}/) && moment(val)) {
          this.vmodel = moment(val).format('YYYY-MM-DD')
        }
      },
    },
  },
  watch: {
    value(val) {
      if (val) this.vmodel = moment(val).format('YYYY-MM-DD')
      else this.vmodel = null
    },
    vmodel(val) {
      // console.log('vmodel', val)
      this.$emit('update:value', val)
      this.$emit('change', val)
    },
  },
  mounted() {
    if (this.useDefault || this.value)
      this.vmodel = moment(this.value).format('YYYY-MM-DD')
  },
}
</script>
<style lang="scss">
.p-datepicker-panel {
  z-index: 10001 !important;
}
.form-inline {
  .ui-datepicker {
    .v-menu__content {
      margin-left: 160px;
    }
    .ui-input {
      display: flex;
      width: 100%;
    }
  }
}
.ui-datepicker {
  margin-bottom: 8px;
}
.v-picker__title {
  display: none;
}
.v-date-picker-table {
  height: 222px;
}
</style>
