<template>
  <component :is="comtype" v-bind="$props" class="grid-table">
    <!-- Pass on all named slots -->
    <template v-for="(_, name) in $slots" #[name]="slotData"
      ><slot :name="name" v-bind="slotData"
    /></template>
  </component>
</template>
<script>
import GridDefault from './Default.vue'
import GridMobile from './Mobile.vue'
export default {
  components: {
    GridDefault,
    GridMobile,
  },
  props: {
    id: {
      type: String,
      default: () => {
        if (!window.uuid) window.uuid = 0
        return 'tbl-' + window.uuid++
      },
    },
    mobile: {
      type: Boolean,
      default: false,
    },
    datagrid: {
      type: Array,
      default: () => [],
    },
    columns: {
      type: Array,
      default: () => [],
    },
    filter: {
      type: Function,
      default: null,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    editMode: {
      type: Array,
      default: () => [],
    },
    dbref: {
      type: String,
      default: '',
    },
    dbparams: {
      type: Object,
      default: () => ({}),
    },
    offline: {
      type: Boolean,
      default: false,
    },
    height: {
      type: String,
      default: 'auto',
    },
    width: {
      type: String,
      default: 'auto',
    },
    groupBy: {
      type: String,
      default: '',
    },
    onEdit: {
      type: Function,
      default: null,
    },
    selectedRow: {
      type: Number,
      default: -1,
    },
    requires: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    autopaging: {
      type: Boolean,
      default: true,
    },
    preHead: {
      type: Boolean,
      default: false,
    },
    doPrint: {
      type: Number,
      default: 0,
    },
    doRebind: {
      type: Number,
      default: 0,
    },
    hasChild: {
      type: Boolean,
      value: false,
    },
  },
  computed: {
    comtype() {
      return this.isMobile && this.mobile ? 'GridMobile' : 'GridDefault'
    },
  },
}
</script>
