const express = require('express')
const router = express.Router()
const Reporter = require('./generator')
const path = require('path')
const fs = require('fs')
const JSZip = require('jszip')
const carbone = require('carbone')
let db = require('../../common/db')
const moment = require('moment')
const Excel = require('exceljs')
const shell = require('shelljs')
const axios = require('axios')
const custom = require('./custom')
const HtmlGenerator = require('./HtmlGenerator')
const { authMiddleware } = require('../auth')

moment.locale('id')

// CARBONE specific setup
carbone.addFormatters({
  // this formatter can be used in a template with {d.myBoolean:yesOrNo()}
  format: function (val, fmt) {
    if (val instanceof Date) {
      return moment(val).format(fmt.replace(/_/g, ' '))
    } else if (!isNaN(val)) {
      return parseFloat(val)
        .toFixed(2)
        .replace(/\d(?=(\d{3})+\.)/g, '$&.')
        .replace(/\.00$/, ' ')
    } else {
      return val
    }
  },
  boolstr: function (val, fmt) {
    const noyes = fmt.split('|')
    if (val === undefined || val === null || val === '') return ''
    else if (val === 0 || val === false) return noyes[0]
    else return noyes[1]
  }
})

const report = {
  colswidth: {
    No: 6,
    NIK: 17,
    Nama: 24,
    KRT_Nama: 24,
    Alamat: 50,
    Kabupaten: 15,
    Kecamatan: 15,
    Kelurahan: 15,
    Desa: 15
  },
  async Generic(req, res, type) {
    const param = { ...req.query, ...req.body }
    try {
      res.header(
        'Cache-Control',
        'private, no-cache, no-store, must-revalidate'
      )
      res.header('Expires', '-1')
      res.header('Pragma', 'no-cache')

      const rptName = param.rptname ? param.rptname : 'Report'
      const dd = moment().format('mmss')
      const filename = rptName.replace(/[^a-z0-9]/gi, '_') + dd
      const options = {
        filename: `tmp/${filename}.${type || 'xlsx'}`,
        useStyles: true,
        useSharedStrings: true
      }
      let workbook = {}
      if (type === 'html') workbook = new HtmlGenerator(options)
      else workbook = new Excel.stream.xlsx.WorkbookWriter(options)

      let sets = 1
      let i = 0
      if (param.sets) sets = param.sets

      do {
        const postfix = i > 0 ? '_s' + (i + 1) : ''
        // console.log(param.sp + postfix)
        // let data = await db.exec(param.sp + postfix, param);
        // console.log(`Finished loading ${data.count} rows.`)
        await report.GenericRun(param.sp + postfix, param, workbook, {
          name: rptName,
          sheet: i,
          headers: param.headers,
          groupsheet: param.groupsheet
        }, param.data).catch(err => {
          console.error(param.sp + postfix)
          throw err
        })

        i++
      } while (i < sets)

      await workbook.commit()
      // console.log('all done')
      if (type === 'pdf' && this.GeneratePDF(`tmp/${filename}.xlsx`)) {
        res.send({
          success: true,
          data: `/get/${filename}.pdf`,
          message: 'Report Generated',
          type: 'url'
        })
      } else {
        res.send({
          success: true,
          data: `/get/${filename}.${type || 'xlsx'}`,
          message: 'Report Generated',
          type: 'url'
        })
      }
    } catch (err) {
      console.error(param.sp)
      res.send({
        success: false,
        message: err.message
      })
    }
  },
  GeneratePDF(path) {
    const currdir = __dirname.replace(/api.reports/, '')
    let libre = '/opt/libreoffice7.5/program/soffice'
    let sep = '/'
    if (!fs.existsSync(libre)) {
      libre = '"C:\\Program Files\\LibreOffice\\program\\soffice.exe"'
      sep = '\\'
    }

    console.info(`Converting to PDF: ${currdir}${sep}${path}..`)
    // console.log(`${libre} --convert-to pdf "${currdir}${sep}${path}" --outdir "${currdir}${sep}tmp"`)
    const out = shell.exec(
      `${libre} --convert-to pdf "${currdir}${sep}${path}" --outdir "${currdir}${sep}tmp"`,
      { silent: true }
    )
    if (out.code !== 0) {
      console.error(`ERR: ${out.stderr}`)
      return false
    } else return true
  },
  GenerateHeader(sheet, keys) {
    const sHeader = {
      border: {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      },
      font: { size: 10, bold: true },
      alignment: { vertical: 'middle', horizontal: 'center' }
    }
    // Generating Dynamic Headers
    for (let i = 0; i < keys.length; i++) {
      if (keys[i].match(/^_/)) continue
      let colspan = 1
      let j = i
      let c1 = ''
      let c2 = ''
      do {
        if (j + 1 >= keys.length) break
        c1 = keys[j].split('_', 2)
        c2 = keys[j + 1].split('_', 2)
        if (c1[0] === c2[0]) colspan++
        j++
      } while (c1[0] === c2[0])

      if (colspan === 1) {
        sheet.mergeCells(1, i + 1, 2, i + 1)
        sheet.getCell(1, i + 1).value = keys[i].replace(/_/g, ' ')
        if (!sheet.getColumn(i + 1).width) {
          sheet.getColumn(i + 1).width =
            this.colswidth[keys[i]] || Math.ceil(keys[i].length * 1.5)
        }
        // console.log(keys[i].replace(/_/g, ' '))
        sheet.getCell(1, i + 1).border = sHeader.border
        sheet.getCell(2, i + 1).alignment = sHeader.alignment
      } else {
        // // array_push(hcol, ['value'=>c1[0], 'colspan'=>colspan]);
        sheet.mergeCells(1, i + 1, 1, i + colspan)
        sheet.getCell(1, i + 1).value = c1[0].replace(/_/g, ' ')
        // console.log(c1[0].replace(/_/g, ' '))
        sheet.getCell(1, i + 1).border = sHeader.border
        sheet.getCell(1, i + 1).alignment = sHeader.alignment
        const len = colspan + i
        while (i < len) {
          const c2 = keys[i].split('_', 2)
          sheet.getCell(2, i + 1).value = c2[1].replace(/_/g, ' ')
          // console.log(c2[1].replace(/_/g, ' '))
          sheet.getCell(2, i + 1).border = sHeader.border
          sheet.getCell(2, i + 1).alignment = sHeader.alignment
          i++
        }
        i--
      }
    }
    sheet.getRow(2).font = sHeader.font
    sheet.getRow(1).font = sHeader.font
  },
  async GenericRun(sp, param, workbook, opts, dbres) {
    // console.log(dbres)
    const sHeader = {
      border: {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      },
      font: { size: 10, bold: true },
      alignment: { vertical: 'middle', horizontal: 'center' }
    }
    const hasPagination = await db.spHasPagination(sp)
    // console.log('GenericRun')
    try {
      // const i = 0
      let ii = 0
      let pageRef = 0
      // Common columns width
      if (opts.groupsheet) {
        // console.log(': with groupsheet')
        const groupCol = opts.groupsheet
        let groupVal = ''

        let sheet = null
        if (!dbres) dbres = await db.exec(sp, param)
        let rptsettings = {
          // hide: ["col1", "col2"],
          // groupsheet: "GroupSheet",
          // styles: 
          // multipleResult
        }
        let res = []
        if (dbres.length > 1 && dbres[0].length > 0 && dbres[0][0]._Settings) {
          rptsettings = JSON.parse(dbres[0][0]._Settings)
          if (dbres.length > 3) {
            for (let rx = 1; rx < dbres.length - 1; rx++) {
              // console.log(rx, Array.isArray(dbres[rx]), dbres[rx].length)
              if (Array.isArray(dbres[rx])) await report.GenericRun(sp, param, workbook, opts, [dbres[0], dbres[rx]])
            }
            return
          } else {
            res = dbres[1]
          }
        } else {
          res = dbres
        }
        let row = res[ii]

        if (!res.length) {
          console.log('NO DATA: ' + sp, param)
          return
        }

        let keys = Object.keys(res[0])
        if (rptsettings.hide) keys = keys.filter(k => !rptsettings.hide.includes(k))

        // COLUMNS SETUP
        if (rptsettings.styles) {
          let i = 1
          for (const key of keys) {
            if (rptsettings.styles[key]) {
              const styles = rptsettings.styles[key]
              for (const style of Object.keys(styles)) {
                sheet.getColumn(i)[style] = styles[style]
              }
            }
            i++
          }
        }

        do {
          if (groupVal !== row[groupCol]) {
            groupVal = row[groupCol]
            // if(sheet) sheet.commit()
            sheet = workbook.addWorksheet(groupVal)
            this.GenerateHeader(sheet, keys)
          }

          const rcolArr = []
          for (const key of keys) {
            if (key.match(/^_/)) rcolArr.push('')
            else rcolArr.push(row[key])
          }
          sheet.addRow(rcolArr) // body
          if (ii < res.length) {
            ii++
            row = res[ii]
          } else if (hasPagination) {
            ii = 0
            pageRef++
            // console.log(`page ${pageRef}: ${sp}`)
            res = await db.exec(sp, {
              ...param,
              _PageRef: pageRef,
              _CountRef: 10000
            })
            if (res.length === 10000 && hasPagination) row = res[ii]
            else row = null
          } else {
            row = null
          }
        } while (row)
      } else {
        // console.log(': no groupsheet')
        if (!dbres) dbres = await db
          .exec(sp, { ...param, _PageRef: pageRef, _CountRef: 50000 }, true)
          .catch(err => console.error(err))
        let rptsettings = {}
        // console.log('rptsettings', res.length, res[0].length, res[0][0]._Settings)
        // console.log(res)
        let res = []
        if (dbres.length > 1 && dbres[0].length > 0 && dbres[0][0]._Settings) {
          rptsettings = JSON.parse(dbres[0][0]._Settings)

          if (rptsettings.groupsheet) {
            opts.groupsheet = rptsettings.groupsheet
            await report.GenericRun(sp, param, workbook, opts, dbres)
            return
          }
          res = dbres[1]
        } else {
          res = dbres
        }
        if (!res.length) {
          console.log('NO DATA: ' + sp, param)
          return
        }
        if (res.length > 0) {
          // console.log(`generating ${res.length} rows`)
          let keys = Object.keys(res[0])
          if (rptsettings.hide) keys = keys.filter(k => !rptsettings.hide.includes(k))
          const sheet = workbook.addWorksheet('Report')

          // PAGE SETUP
          sheet.pageSetup.fitToPage = true
          sheet.pageSetup.fitToWidth = 1
          sheet.pageSetup.fitToHeight = 0
          sheet.pageSetup.paperSize = 9
          sheet.pageSetup.orientation = 'landscape'
          sheet.pageSetup.margins = {
            left: 0.25,
            right: 0.25,
            top: 0.5,
            bottom: 0.5,
            header: 0.3,
            footer: 0.3
          }
          // const i = 0
          let ii = 0
          let pageRef = 0
          let row = res[ii]

          // COLUMNS SETUP
          if (rptsettings.styles) {
            let i = 1
            for (const key of keys) {
              if (rptsettings.styles[key]) {
                const styles = rptsettings.styles[key]
                for (const style of Object.keys(styles)) {
                  sheet.getColumn(i)[style] = styles[style]
                }
              }
              i++
            }
          }

          if (opts.headers) {
            sheet.addRow(
              opts.headers.map((h, idx) => {
                sheet.getColumn(idx + 1).width = this.colswidth[h.value]
                  ? this.colswidth[h.value]
                  : String(row[h.value]).length * 1.3 < 6
                    ? 6
                    : String(row[h.value]).length * 1.3
                return h.name
              })
            )
            sheet.getRow(1).font = sHeader.font
            sheet.getRow(1).commit()

            do {
              const rcolArr = []
              for (const i in opts.headers) {
                rcolArr.push(row[opts.headers[i].value])
              }
              sheet.addRow(rcolArr).commit() // body
              if (ii < res.length) {
                ii++
                row = res[ii]
              } else {
                ii = 0
                pageRef++
                // console.log(`page ${pageRef}`)
                res = await db.exec(sp, {
                  ...param,
                  _PageRef: pageRef,
                  _CountRef: 50000
                })
                if (res.length === 50000) row = res[ii]
                else row = null
              }
            } while (row)
          } else {
            this.GenerateHeader(sheet, keys)
            do {
              const rcolArr = []
              for (const key of keys) {
                rcolArr.push(row[key])
              }
              const r = sheet.addRow(rcolArr)
              if (rptsettings.ordrStyles && row.Ordr && rptsettings.ordrStyles[row.Ordr]) {
                const styles = rptsettings.ordrStyles[row.Ordr]
                for (const style of Object.keys(styles)) {
                  r[style] = styles[style]
                }
              }
              r.commit()
              if (ii < res.length) {
                ii++
                row = res[ii]
              } else if (hasPagination) {
                ii = 0
                pageRef++
                // console.log(`page ${pageRef}: ${sp}`)
                res = await db.exec(sp, {
                  ...param,
                  _PageRef: pageRef,
                  _CountRef: 50000
                })
                if (res.length === 50000 && hasPagination) row = res[ii]
                else row = null
              } else {
                row = null
              }
            } while (row)
          }
          sheet.commit()
          // console.log('done')
        }
      }
    } catch (ex) {
      console.error(ex)
    }
  }
}

router.post('/GetParamsVue', async function (req, res) {
  const dbp = await db.pool.query(
    `SELECT * FROM information_schema.PARAMETERS
    WHERE SPECIFIC_SCHEMA = '${db.dbname}'
    AND SPECIFIC_NAME = '${req.body.sp}' AND PARAMETER_MODE = 'IN'`
  )

  const sb = []
  for (const idx in dbp) {
    const paramName = dbp[idx].PARAMETER_NAME // .substr(1);
    if (paramName.substr(-3) !== 'Ref') {
      const o = {}
      o.id = paramName
      if (paramName.substr(-2) === 'ID') {
        o.text = paramName.substr(8, paramName.length - 10)
      } else { o.text = paramName.substr(1) }

      if (paramName.substr(-2) === 'ID') {
        // sb +=
        //   '"url":"api/call/' +
        //   paramName.substr(1, paramName.length - 3) +
        //   '",';
        o.dbref = paramName.substr(1, paramName.length - 3)
        // db += '"Type":"Select",';
        o.type = 'XSelect'
      } else if (
        dbp[idx].DATA_TYPE === 'varchar'
      ) {
        // db += '"Type":"Input",';
        o.type = 'XInput'
      } else if (
        dbp[idx].DATA_TYPE === 'datetime' ||
        dbp[idx].DATA_TYPE === 'date'
      ) {
        // db += '"Type":"Date",';
        o.type = 'DatePicker'
      } else if (
        dbp[idx].DATA_TYPE === 'boolean' ||
        dbp[idx].DATA_TYPE === 'tinyint'
      ) {
        // db += '"Type":"Checkbox",';
        o.type = 'Checkbox'
      } else {
        db += '"Type":"' + dbp[idx].DATA_TYPE + '"'
      }
      // db += "},";
      sb.push(o)
    }
  }
  // sb += "{";
  // sb += '"Id":"btnReport",';
  // sb += '"Name":"",';
  // sb += '"Type":"VBtn"';
  // sb += "}";
  // sb += "]";

  res.send({
    success: true,
    data: sb,
    message: '',
    type: 'array'
  })
})

router.post('/GetParams', async function (req, res) {
  const dbp = await db.pool.query(
    `SELECT * FROM information_schema.PARAMETERS
    WHERE SPECIFIC_SCHEMA = '${db.dbname}'
    AND SPECIFIC_NAME = '${req.query.sp}' AND PARAMETER_MODE = 'IN'`
  )

  let sb = '['
  for (const idx in dbp) {
    const paramName = dbp[idx].PARAMETER_NAME // .substr(1);
    if (paramName.substr(-3) !== 'Ref') {
      sb += '{'
      sb += '"Id":"' + paramName + '",'
      if (paramName.substr(-2) === 'ID') { sb += '"Name":"' + paramName.substr(8, paramName.length - 10) + '",' } else sb += '"Name":"' + paramName.substr(1) + '",'
      if (paramName.substr(-2) === 'ID') {
        sb +=
          '"url":"api/call/' +
          paramName.substr(1, paramName.length - 3) +
          '",'
        db += '"Type":"select",'
      } else if (
        dbp[idx].DATA_TYPE === 'datetime' ||
        dbp[idx].DATA_TYPE === 'varchar'
      ) {
        db += '"Type":"text",'
      } else if (
        dbp[idx].DATA_TYPE === 'datetime' ||
        dbp[idx].DATA_TYPE === 'date'
      ) {
        db += '"Type":"date",'
      } else if (
        dbp[idx].DATA_TYPE === 'boolean' ||
        dbp[idx].DATA_TYPE === 'tinyint'
      ) {
        db += '"Type":"checkbox",'
      } else {
        db += '"Type":"' + dbp[idx].DATA_TYPE + '"'
      }
      db += '},'
    }
  }
  sb += '{'
  sb += '"Id":"btnReport",'
  sb += '"Name":"",'
  sb += '"Type":"button"'
  sb += '}'
  sb += ']'

  res.send({
    Success: true,
    Data: sb,
    Message: '',
    Type: 'array'
  })
})

router.post('/Generic/:type', authMiddleware, async (req, res) => {
  req.setTimeout(600000)
  // let rid = req.query.rid.split("/");
  await report.Generic(req, res, req.params.type)
})

router.get('/get/backlog/:file', async function (req, res) {
  res.sendFile(path.resolve('tmp/backlog/' + req.params.file))
})

router.get('/get/generated/:file', async function (req, res) {
  res.sendFile(path.resolve('tmp/mysqlfiles/' + req.params.file))
})

router.get('/get/:file', async function (req, res) {
  res.sendFile(path.resolve('tmp/' + req.params.file))
})

router.get('/get/templates/:file', async function (req, res) {
  res.sendFile(path.resolve('tmp/templates/' + req.params.file))
})

router.post('/template/backlog/:file', async function (req, res) {
  let file = 'backlog/' + req.params.file
  renderTemplate(file, req, res)
})

router.post('/template/:file', async function (req, res) {
  let { file } = req.params
  renderTemplate(file, req, res)
})

const download = (url, image_path) => {
  return axios({
    url,
    responseType: 'stream',
  }).then(
    response =>
      new Promise((resolve, reject) => {
        if(image_path) {
          response.data
            .pipe(fs.createWriteStream(image_path))
            .on('finish', () => resolve())
            .on('error', e => reject(e));
        } else {
          resolve(response.data)
        }
      }),
  );
}

const renderTemplate = async (file, req, res) => {
  // let {file} = req.params
  let data = await db.exec(req.body.sp, req.body, { returnAll: true })
  // console.log(data);
  if (req.body.renderEngine === 'text') {
    data = data[0]
    const template = fs.readFileSync('tmp/templates/' + file, 'utf-8')
    let result = ''
    for (const d of data) {
      let txt = template
      for (const c in d) {
        txt = txt.replace(new RegExp(`{{${c}}}`), d[c] || '')
      }
      result += txt
    }
    fs.writeFileSync('tmp/' + file, result)
    res.send({
      success: true,
      data: `/get/${file}`,
      message: 'Report Generated',
      type: 'url'
    })
  } else {
    if (req.body.transformResult) {
      let rs = data
      data = {
        ...rs[0][0]
      }
      for (let k in req.body.transformResult) {
        data[k] = rs[req.body.transformResult[k]]
      }
      let needParse = []
      for (let key in data) {
        if (key.match(/^_json_/)) {
          needParse.push(key)
          // console.log(d[0][key])
        }
      }
      if (needParse.length) {
        for (let key of needParse) {
          let newKey = key.replace(/^_json_/, '')
          data[newKey] = JSON.parse(data[key])
          delete data[key]
        }
      }
    } else {
      data = data[0]
    }

    let convert = {}
    if (file.match(/.ods$/)) {
      res.header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
      convert = { convertTo: 'xlsx' }
    }

    carbone.render('tmp/templates/' + file, data, convert, async (
      err,
      result
    ) => {
      if (err) {
        return console.error(err)
      }
      if (req.body.imgcols) {
        let p = new Promise((resolve, reject) => {
          const zip = new JSZip();
          zip.loadAsync(result).then((z) => {
            for (let d of data) {
              for (let k of req.body.imgcols) {
                let img = fs.readFileSync(path.resolve(d[k].replace('https://simperum.disperakim.jatengprov.go.id', '').replace(/^\//, '')))
                if (file.match(/\.ppt/))
                  z.file("ppt/media" + d[k], img)
                else if (file.match(/\.xls/))
                  z.file("xl/media" + d[k], img)
              }
            }
            z.generateAsync({ type: "nodebuffer" }).then((blob) => {
              resolve(blob);
            })
          })
        })
        result = await p
      }
      if (req.body.hasImage) {
        const rxx = {
          ppt: {
            media: "ppt/media",
            d: 'slides/'
          },
          xls: {
            media: "xl/media",
            d: 'drawings/'
          }
        }
        let rx = null
        if (file.match(/\.ppt/))
          rx = rxx.ppt
        else if (file.match(/\.xls/))
          rx = rxx.xls

        let imgCount = 1;
        let p = new Promise((resolve, reject) => {
          const zip = new JSZip();
          zip.loadAsync(result).then(async (z) => {
            for (; ;) {
              if (!z.files[rx.media + `/image${imgCount}.jpeg`]) break;
              imgCount++
            }
            for (let f of Object.keys(z.files)) {
              if (f.match(/^(xl\/drawings|ppt\/slides)\/[\w\d]+.xml$/)) {
                if (z.files[f.replace(rx.d, rx.d + '_rels/') + '.rels']) {
                  console.log(f)
                  let t = await z.files[f].async('text')
                  let tx = await z.files[f.replace(rx.d, rx.d + '_rels/') + '.rels'].async('text')
                  let mx = t.matchAll(/title="(\/uploads[\w\/\d.]+)"(((?!rId\d).)+)="(rId\d)"/g)
                  mx = [...mx]
                  if (!mx.length) mx = t.matchAll(/descr="(\/uploads[\w\/\d.]+)"(((?!rId\d).)+)="(rId\d)"/g)
                  for (let m of mx) {
                    if (m) {
                      let igmName = `image${imgCount}.jpeg`
                      // tx = tx.replace(new RegExp(`(Id="${m[4]}"(((?!rId\\d).)+)relationships/image"\\s)Target="([\\w/\\d.]+)"`), `$1Target="../media/${m[1].replace(/^\//,'')}"`)
                      tx = tx.replace(new RegExp(`(Id="${m[4]}"(((?!rId\\d).)+)relationships/image"\\s)Target="([\\w/\\d.]+)"`), `$1Target="../media/${igmName}"`)
                      let img = null
                      if (!fs.existsSync(path.resolve(m[1].replace('/ori/', '/med/').replace(/^\//, '')))) {
                        console.log(igmName, m[1].replace('/ori/', '/med/').replace(/^\//, ''))
                        img = await download('http://simperum.disperakim.jatengprov.go.id/' + m[1].replace('/ori/', '/med/').replace(/^\//, ''))
                        // fs.writeFileSync(path.resolve(m[1].replace('/ori/', '/med/').replace(/^\//, '')), img)
                      } else {
                        img = fs.readFileSync(path.resolve(m[1].replace('/ori/', '/med/').replace(/^\//, '')))
                      }
                      // z.file(rx.media+m[1], img)
                      z.file(rx.media + '/' + igmName, img)
                      imgCount++
                    }
                  }
                  z.file(f.replace(rx.d, rx.d + '_rels/') + '.rels', tx)
                }
              }
            }

            z.generateAsync({ type: "nodebuffer" }).then((blob) => {
              resolve(blob);
            })
          })
        })
        result = await p
      }
      // write the result
      let m = moment().format('HHmmss')
      if (file.match(/.ods$/)) {
        file = file.replace(/\.\w{3,4}$/, '.xlsx')
        fs.writeFileSync('tmp/' + file, result)
      }
      fs.writeFileSync('tmp/' + file, result)
      if (req.body.out == 'pdf') {
        report.GeneratePDF(`tmp/${file}`)
        file = file.replace(/\.\w{3,4}$/, '.pdf')
      }
      res.send({
        success: true,
        data: `/get/${file}`,
        message: 'Report Generated',
        type: 'url'
      })
    })
  }
}

router.post('/generate/:type', async function (req, res) {
  try {
    if (req.query.sp || req.body.sp || (req.body.headers && req.body.data)) {
      await report.Generic(req, res, req.params.type)
    } else {
      res.send({
        success: true,
        data: null,
        message: 'Cant Generate Report',
        type: 'error'
      })
    }
  } catch (ex) {
    res.send({
      success: false,
      data: ex.message,
      message: 'Error while generating report',
      type: 'error'
    })
  }
})

router.post('/custom/:rpt', async function (req, res) {
  if (req.params.rpt) {
    const filename = await Reporter.Render(custom[req.params.rpt], req.body)
    res.send({
      success: true,
      data: `/get/${filename}.xlsx`,
      message: 'Report Generated',
      type: 'url'
    })
  } else {
    res.send({ success: false })
  }
})

module.exports = router
