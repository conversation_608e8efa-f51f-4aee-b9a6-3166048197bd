<template>
  <Modal title="Pesan" v-model:show="xshow" submitText="KIRIM" @onSubmit="Send">
    <MessageList :items="items" @delete="Delete" />
    <div style="padding:8px 0;">
      <div style="font-size:12px;">
        {{ items.length ? 'Balas:' : '<PERSON><PERSON>:' }}
      </div>
      <TextArea width="300px" v-model:value="message" />
    </div>
  </Modal>
</template>
<script>
import MessageList from './MessagesList.vue'
export default {
  components: {
    MessageList,
  },
  data: () => ({
    xshow: false,
    message: '',
    items: [],
  }),
  props: {
    show: Boolean,
    noRef: [Number, String],
    tahun: [Number, String],
  },
  watch: {
    show(val) {
      if (val) this.popuplate()
      else {
        this.items = []
        this.message = ''
      }
      this.xshow = val
    },
    xshow(val) {
      this.$emit('update:show', val)
    },
  },
  methods: {
    async popuplate() {
      let { data } = await this.$api.call('PRM_SelMessages', {
        _NoRef: this.noRef,
        _Tahun: this.tahun,
      })
      this.items = data
    },
    async Send() {
      await this.$api.call('PRM_SavMessages', {
        _NoRef: this.noRef,
        _Tahun: this.tahun,
        _Message: this.message,
      })
      this.xshow = false
    },
    async Delete(id) {
      await this.$api.call('PRM_DelMessages', {
        _MessageID: id,
      })
      this.popuplate()
    },
  },
}
</script>
<style lang="scss"></style>
