<template>
  <Page title="Dokumen" :sidebar="true">
    <div style="background: white; overflow-x: hidden; overflow-y: auto">
      <div style="padding: 10px">
        <!-- <XSelect
          :items="[
            { val: 'masuk', txt: 'SURAT MASUK' },
            // { val: 'keluar', txt: 'SURAT KELUAR' },
          ]"
          v-model:value="jenisSurat"
          width="100%"
          style="margin-right: 10px"
        /> -->
        <!-- <div class="tab-container">
          <input type="radio" name="tab" id="tab1" class="tab tab--1" />
          <label class="tab_label" for="tab1">Surat Masuk</label>

          <input type="radio" name="tab" id="tab2" class="tab tab--2" />
          <label class="tab_label" for="tab2">Surat Keluar</label>

          <div class="indicator"></div>
        </div> -->
      </div>

      <List
        v-model:items="suratList"
        @itemClick="SuratClicked"
        :selectOnLoad="true"
        style="height: calc(100vh - 220px); overflow-y: auto"
      >
        <template #default="{ row }">
          <v-list-item>
            <v-list-item-content>
              <v-list-item-title style="display: flex">
                <span
                  style="
                    max-width: calc(100% - 55px);
                    overflow: hidden;
                    text-overflow: ellipsis;
                  "
                >
                  {{ row.NoSurat || '(Tanpa Nomor)' }}
                </span>
                <!-- <v-icon
                  small
                  style="margin-top: -2px; margin-left: 10px"
                  color="primary"
                  v-tooltip="'Sudah Disposisi'"
                  v-show="row.Disposisi"
                >
                  mdi-account-multiple-check
                </v-icon> -->
                <v-spacer />
                <v-btn variant="outlined" x-small rounded v-show="row.Disposisi">
                  {{ row.JmlLaporan }} / {{ row.Disposisi?.split(',').length }}
                </v-btn>
              </v-list-item-title>
              <v-list-item-subtitle>
                {{ row.Keterangan }}
              </v-list-item-subtitle>
            </v-list-item-content>
          </v-list-item>
        </template>
      </List>
      <div style="padding: 10px">
        <v-btn color="primary" style="width: 100%" @click="TambahSurat">
          <v-icon>mdi-plus</v-icon>
          TAMBAH BARU
        </v-btn>
      </div>
    </div>
    <div>
      <div
        class="form-inline"
        style="max-width: 100%; width: 600px; padding: 0 10px"
      >
        <XInput label="No Surat" v-model:value="form.NoSurat" />
        <DatePicker label="Tgl Surat" v-model:value="form.TglSurat"></DatePicker>
        <XSelect
          label="Jenis Surat"
          :items="[
            { val: 'individu', txt: 'Individu' },
            { val: 'kelompok', txt: 'Kelompok' },
            { val: 'pemberitahuan', txt: 'Pemberitahuan' },
          ]"
          v-model:value="form.Jenis"
          width="calc(100% + 10px)"
        />
        <br />
      </div>
      <div
        style="
          max-width: 100%;
          width: 600px;
          padding: 0 10px;
          position: relative;
        "
      >
        <XInput placeholder="Asal Surat" v-model:value="form.Asal" />
        <XInput placeholder="Perihal" v-model:value="form.Perihal" />
        <TextArea
          placeholder="Keterangan Acara"
          v-model:value="form.Keterangan"
          width="340px"
          height="120px"
        />
        <TextArea
          placeholder="Catatan Kasie"
          v-model:value="form.Catatan"
          width="340px"
          height="120px"
        />
        <!-- <v-btn
          style="width: 100%"
          color="warning"
          variant="outlined"
          small
          v-show="isReading"
        >
          <v-icon left>mdi-auto-fix</v-icon>
          Sedang Membaca surat ..
        </v-btn> -->
        <div class="animated-border-box-glow" v-show="isReading"></div>
        <div class="animated-border-box" v-show="isReading">
          <!-- Inside the Box -->
        </div>
        <Uploader
          mode="form"
          :downloadText="isReading ? 'MEMBACA SURAT..' : 'Download'"
          v-model:value="form.SuratUrl"
          accept="application/pdf"
          @change="parseFile"
        />
        <br />
        <div v-show="form.SuratID">
          <div>Disposisi</div>
          <div style="max-height: 400px; overflow-y: auto">
            <v-list-item v-for="p in pegawai" :key="p.PegawaiID">
              <template>
                <Checkbox
                  :text="p.FullName"
                  v-model:value="p.IsChecked"
                  @click="DispoCheck($event, p)"
                ></Checkbox>
              </template>
            </v-list-item>
          </div>
        </div>
        <br />
        <v-btn color="primary" @click="Save" class="mr-4"> Simpan </v-btn>
        <v-btn color="error" @click="Delete" class="mr-4"> Hapus </v-btn>
      </div>
      <!-- <object
        :data="fileUrl"
        type="application/pdf"
        width="100%"
        height="1200px"
      >
        <p>
          File tidak ditemukan, silahkan upload ulang <br />
          {{ this.$api.url + this.form.SuratUrl }}
        </p>
      </object> -->
      <!-- <iframe
        :src="this.$api.url + this.form.SuratUrl"
        width="100%"
        height="80%"
      >
      </iframe> -->
    </div>
  </Page>
</template>
<script>
import { mapActions } from 'vuex'
import moment from 'moment'
export default {
  data: () => ({
    suratList: [],
    pegawai: [],
    disposisi: [],
    form: {},
    isReading: false,
    jenisSurat: 'masuk',
    keyword: '',
  }),
  computed: {
    fileUrl() {
      return this.$api.url + this.form.SuratUrl
    },
  },
  created() {
    const id = this.$route.query.id
    if (id) {
      this.Populate(id)
      this.PopulatePegawai(id)
      this.setPageFocused(true)
    }
  },
  mounted() {
    this.PopulateSideBar()
    this.PopulatePegawai(0)
  },
  methods: {
    ...mapActions(['setPageFocused']),
    SuratClicked(row) {
      this.disposisi = []
      this.form = row
      this.PopulatePegawai(row.SuratID)
      this.setPageFocused(true)
    },
    async Delete() {
      if (!confirm('Anda yakin akan menghapus data ini?')) return

      let ret = await this.$api.call('DOC.DelSuratMasuk', {
        SuratID: this.form.SuratID,
      })
      if (ret.success) {
        this.PopulateSideBar()
        this.form = {}
      }
    },
    async Populate(id) {
      let surat = await this.$api.call('DOC.SelSuratMasuk', { SuratID: id })
      this.form = surat.data[0]
    },
    async PopulateSideBar() {
      let surats = await this.$api.call('DOC.SelSuratMasuk', { nocache: true })
      // console.log(surats.data)
      this.suratList = surats.data
      // console.log('suratList', this.suratList)
    },
    async PopulatePegawai(SuratID) {
      this.pegawai = []
      let ps = await this.$api.call('DOC.SelPegawai', { SuratID })
      this.pegawai = ps.data
    },
    async parseFile() {
      // if (confirm('Apakah surat ini bersifat rahasia?')) return

      this.isReading = true
      let d = await this.$api.post('/api/docs/parse', {
        ...this.form,
      })
      this.isReading = false
      if (d.success) {
        this.form = {
          ...this.form,
          NoSurat: this.form.NoSurat || d.data.NoSurat,
          TglSurat:
            this.form.TglSurat || moment(d.data.TglSurat).format('YYYY-MM-DD'),
          Asal: this.form.Asal || d.data.Asal,
          Perihal: this.form.Perihal || d.data.Perihal,
          Keterangan: this.form.Keterangan || d.data.Keterangan,
        }
      }
    },
    async Save() {
      this.form.Disposisi = this.pegawai
        .filter((p) => p.IsChecked)
        .map((p) => p.PegawaiID)
        .join(',')
      let ret = await this.$api.call('DOC.SavSuratMasuk', this.form)
      if (!this.form.SuratID) {
        this.$api.post('/api/docs/notify-new', {
          SuratID: ret.data?.[0]?.SuratID,
          // ...this.form,
        })
      }
      if (ret.success) {
        this.PopulateSideBar()
      }
    },
    TambahSurat() {
      this.form = {}
      this.PopulatePegawai(0)
      this.setPageFocused(true)
    },
  },
}
</script>
<style lang="scss">
.rowlnk {
  border-bottom: 1px solid #ddd;
}
.rowgrp {
  border-bottom: 1px solid #ddd;
  font-weight: bold;
  font-size: small;
  padding: 5px;
  background: #f3f3f3;
}
.page-dokumen {
  .--input,
  textarea,
  .ui-upload {
    width: 100% !important;
  }
}

/* From Uiverse.io by zanina-yassine */
/* Remove this container when use*/
.component-title {
  width: 100%;
  position: absolute;
  z-index: 999;
  top: 30px;
  left: 0;
  padding: 0;
  margin: 0;
  font-size: 1rem;
  font-weight: 700;
  color: #888;
  text-align: center;
}

.tab-container {
  position: relative;

  display: flex;
  flex-direction: row;
  align-items: flex-start;

  padding: 2px;

  background-color: #dadadb;
  border-radius: 9px;
}

.indicator {
  content: '';
  width: 50%;
  height: 28px;
  background: #ffffff;
  position: absolute;
  top: 2px;
  left: 2px;
  z-index: 9;
  border: 0.5px solid rgba(0, 0, 0, 0.04);
  box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.12), 0px 3px 1px rgba(0, 0, 0, 0.04);
  border-radius: 7px;
  transition: all 0.2s ease-out;
}

.tab {
  width: 50%;
  height: 28px;
  position: absolute;
  z-index: 99;
  outline: none;
  opacity: 0;
}

.tab_label {
  width: 50%;
  height: 28px;

  position: relative;
  z-index: 999;

  display: flex;
  align-items: center;
  justify-content: center;

  border: 0;

  font-size: 0.75rem;
  opacity: 0.6;

  cursor: pointer;
}

.tab--1:checked ~ .indicator {
  left: 2px;
}

.tab--2:checked ~ .indicator {
  left: calc(50% - 2px);
}

.tab--3:checked ~ .indicator {
  left: calc(50% * 2 - 10px);
}

/*// Glow Border Animation //*/

.animated-border-box,
.animated-border-box-glow {
  // max-height: 200px;
  width: 590px;
  max-width: calc(100% - 10px);
  height: 45px;
  width: 100%;
  position: absolute;
  overflow: hidden;
  z-index: 0;
  /* Border Radius */
  border-radius: 10px;
  margin-top: -5px;
  margin-left: -5px;
}

.animated-border-box-glow {
  overflow: hidden;
  /* Glow Blur */
  filter: blur(20px);
}

.animated-border-box:before,
.animated-border-box-glow:before {
  content: '';
  z-index: -2;
  text-align: center;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(0deg);
  position: absolute;
  width: 99999px;
  height: 99999px;
  background-repeat: no-repeat;
  background-position: 0 0;
  /*border color, change middle color*/
  background-image: conic-gradient(
    rgba(0, 0, 0, 0),
    #1976ed,
    rgba(0, 0, 0, 0) 25%
  );
  /* change speed here */
  animation: rotate 4s linear infinite;
}

.animated-border-box:after {
  content: '';
  position: absolute;
  z-index: -1;
  /* border width */
  left: 5px;
  top: 5px;
  /* double the px from the border width left */
  width: calc(100% - 10px);
  height: calc(100% - 10px);
  /*bg color*/
  // background: #292a2e;
  background: white;
  /*box border radius*/
  border-radius: 7px;
}

@keyframes rotate {
  100% {
    transform: translate(-50%, -50%) rotate(1turn);
  }
}

/*// Border Animation END//*/

/*// Ignore This //*/
body {
  margin: 0px;
}

.center-box {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #1d1e22;
}
</style>
