<template>
  <Page title="Pencairan Relokasi" :sidebar="true">
    <template v-slot:toolbar>
      <v-icon @click="doPrint++" v-tooltip="'Download Excel'">
        mdi-microsoft-excel</v-icon
      >
    </template>
    <Sidebar
      v-model:value="area"
      :rebind="rebindSidebar"
      :tabs="[2, 4]"
      :filter="filterArea"
    />
    <div
      style="padding: 10px; width: calc(100vw - 340px)"
      v-show="area.Kelurahan"
    >
      <DesaGlance
        :dbparams="area"
        :jmlUsulan="jmlUsulan"
        @clickTambahBaru="ClickTambahBaru"
      >
        <div>
          <!-- <v-btn variant="text" size="small" color="primary" @click="Download">
            <v-icon left>print</v-icon>
            RPD
          </v-btn> -->
        </div>
      </DesaGlance>
      <div style="padding: 5px; display: flex; width: 100%">
        <v-icon color="primary" style="margin: -5px 5px 0 0">
          mdi-view-grid
        </v-icon>
        <XSelect
          :items="[
            { val: 'standard', txt: 'STANDARD' },
            { val: 'ruspin', txt: 'RUSPIN' },
            { val: 'tokmat', txt: 'TOKMAT' },
          ]"
          v-model:value="rowMode"
        />
        <v-spacer />
        <v-btn size="small" variant="text" @click="OpenDataUmum">
          <v-icon left>mdi-format-list-bulleted</v-icon>
          DATA UMUM
        </v-btn>
        <v-btn
          small
          color="primary"
          @click="OpenChecklist"
          style="margin-left: 5px"
        >
          <v-icon left>mdi-check-decagram-outline</v-icon>
          CHECKLIST
        </v-btn>
        <v-btn size="small" color="warning" @click="OpenRAB" style="margin-left: 5px">
          <v-icon left>mdi-shape-plus</v-icon>
          RAB
        </v-btn>
        <!-- <v-btn size="small" color="primary" @click="Download" style="margin-left:5px;">
          <v-icon left>mdi-cube-send</v-icon>
          MATERIAL
        </v-btn> -->
      </div>
      <Grid
        id="tbl-grid"
        v-model:datagrid="datagrid"
        :dbref="dbref"
        :dbparams="area"
        :filter="filterGrid"
        :autopaging="false"
        :disabled="true"
        :mobile="true"
        :doPrint="doPrint"
        :doRebind="doRebind"
        :requires="['Kabupaten']"
        height="calc(100vh - 260px); width:100%"
        :columns="[
          {
            name: 'No',
            value: 'NoRPD',
            width: '40px',
            hide: rowMode !== 'standard',
          },
          {
            name: 'NIK',
            value: 'NIK',
            class: 'plain left',
            mobilePos: 'top-left',
            width: '175px',
            hide: rowMode !== 'standard',
          },
          {
            name: 'PPHP',
            value: 'Role',
            width: '50px',
            hide: rowMode !== 'standard',
          },
          {
            name: 'Nama',
            value: 'KRT_Nama',
            width: '200px',
            class: 'fix-width',
            mobilePos: 'bottom-left',
          },
          {
            name: 'Alamat',
            value: 'Alamat',
            width: '250px',
            class: 'fix-width',
            hide: rowMode !== 'standard',
          },
          {
            name: 'Skor',
            value: 'ScoreTag',
            width: '100px',
            hide: rowMode !== 'standard',
          },
          {
            name: 'DT',
            value: 'TipeData',
            width: '100px',
            hide: rowMode !== 'standard',
          },
          {
            name: 'Termin',
            value: 'Dokumen',
            class: 'plain center',
            mobilePos: 'bottom-right',
            width: '160px',
            hide: rowMode !== 'standard',
          },
          {
            name: 'No/Tgl SPK',
            value: 'TglSpkTokmat',
            class: 'plain',
            hide: rowMode !== 'tokmat',
          },
          {
            name: 'No/Tgl DO',
            value: 'TglDoTokmat',
            class: 'plain',
            hide: rowMode !== 'tokmat',
          },
          {
            name: 'No/Tgl SPP',
            value: 'TglSppTokmat',
            class: 'plain',
            hide: rowMode !== 'tokmat',
          },
          {
            name: 'No/Tgl BAP',
            value: 'TglBapTokmat',
            class: 'plain',
            hide: rowMode !== 'tokmat',
          },
          {
            name: 'No/Tgl BAST',
            value: 'TglBastTokmat',
            class: 'plain',
            hide: rowMode !== 'tokmat',
          },
          {
            name: 'No/Tgl Mhn Byr',
            value: 'TglMhnByrTokmat',
            class: 'plain',
            hide: rowMode !== 'tokmat',
          },
          {
            name: 'No/Tgl BA Byr',
            value: 'TglBaByrTokmat',
            class: 'plain',
            hide: rowMode !== 'tokmat',
          },
          {
            name: 'No/Tgl SPK',
            value: 'TglSpkRuspin',
            class: 'plain',
            hide: rowMode !== 'ruspin',
          },
          {
            name: 'No/Tgl DO',
            value: 'TglDoRuspin',
            class: 'plain',
            hide: rowMode !== 'ruspin',
          },
          {
            name: 'No/Tgl SPP',
            value: 'TglSppRuspin',
            class: 'plain',
            hide: rowMode !== 'ruspin',
          },
          {
            name: 'No/Tgl BAP',
            value: 'TglBapRuspin',
            class: 'plain',
            hide: rowMode !== 'ruspin',
          },
          {
            name: 'No/Tgl BAST',
            value: 'TglBastRuspin',
            class: 'plain',
            hide: rowMode !== 'ruspin',
          },
          {
            name: 'No/Tgl Mhn Byr',
            value: 'TglMhnByrRuspin',
            class: 'plain',
            hide: rowMode !== 'ruspin',
          },
          {
            name: 'No/Tgl BA Byr',
            value: 'TglBaByrRuspin',
            class: 'plain',
            hide: rowMode !== 'ruspin',
          },
        ]"
      >
        <template #row-IsChecked="{ row }">
          <Checkbox v-model:value="row.IsSelected" checkedIcon="check_box" />
        </template>
        <template #row-Tahapan="{ row }">
          <v-icon v-tooltip="row.Tahapan">
            mdi-numeric-{{
              row.Tahapan ? row.Tahapan.replace('REKOM ', '') : ''
            }}-box
          </v-icon>
        </template>
        <template #row-NIK="{ row }">
          <v-btn
            text
            small
            color="primary"
            @click.stop="OpenDetail(row.NoRef)"
            style="width: 165px; text-align: left"
          >
            {{ row.NIK || '&lt; kosong &gt;' }}
          </v-btn>
          <v-icon
            v-if="IsRekom"
            color="red"
            v-tooltip="'Ganti Penerima'"
            @click.stop="OpenGantiPenerima(row.NIK)"
            >mdi-account-switch-outline</v-icon
          >
        </template>
        <template #row-Role="{ row }">
          <MenuButton
            :menu="['Ketua', 'Sekretaris', 'Anggota', '-']"
            @item-click="ChangeRole($event, row)"
          >
            <template #default="{ on }">
              <v-icon
                :color="row.Panitia ? 'success' : '#ddd'"
                v-on="on"
                v-tooltip="row.Panitia || '-'"
              >
                {{ panitiaIcon[row.Panitia] || 'mdi-circle-outline' }}
              </v-icon>
            </template>
          </MenuButton>
        </template>
        <template #row-TipeData="{ row }">
          {{ parseInt((row.TipeData + '').substr(-2)) + 2000 }}
        </template>
        <template #row-Dokumen="{ row }">
          <!-- <v-btn
            small
            variant="outlined"
            color="success"
            @click.stop="OpenChecklist(row.NIK)"
            >TR #1</v-btn
          > -->
          <v-btn
            small
            variant="outlined"
            color="success"
            @click.stop="OpenChecklist23(row.NIK, 2)"
          >
            TR #2
          </v-btn>
          <v-btn
            small
            variant="outlined"
            color="success"
            @click.stop="OpenChecklist23(row.NIK, 3)"
            style="margin-left: 5px"
          >
            TR #3
          </v-btn>
        </template>
        <template #row-TglSpkTokmat="{ row }">
          <XInput
            v-model:value="row.NoSpkTokmat"
            @change="Update('NoSpkTokmat', row.NIK, $event)"
          />
          <DatePicker
            v-model:value="row.TglSpkTokmat"
            @change="Update('TglSpkTokmat', row.NIK, $event)"
          />
        </template>
        <template #row-TglDoTokmat="{ row }">
          <XInput
            v-model:value="row.NoDoTokmat"
            @change="Update('NoDoTokmat', row.NIK, $event)"
          />
          <DatePicker
            v-model:value="row.TglDoTokmat"
            @change="Update('TglDoTokmat', row.NIK, $event)"
          />
        </template>
        <template #row-TglSppTokmat="{ row }">
          <XInput
            v-model:value="row.NoSppTokmat"
            @change="Update('NoSppTokmat', row.NIK, $event)"
          />
          <DatePicker
            v-model:value="row.TglSppTokmat"
            @change="Update('TglSppTokmat', row.NIK, $event)"
          />
        </template>
        <template #row-TglBapTokmat="{ row }">
          <XInput
            v-model:value="row.NoBapTokmat"
            @change="Update('NoBapTokmat', row.NIK, $event)"
          />
          <DatePicker
            v-model:value="row.TglBapTokmat"
            @change="Update('TglBapTokmat', row.NIK, $event)"
          />
        </template>
        <template #row-TglBastTokmat="{ row }">
          <XInput
            v-model:value="row.NoBastTokmat"
            @change="Update('NoBastTokmat', row.NIK, $event)"
          />
          <DatePicker
            v-model:value="row.TglBastTokmat"
            @change="Update('TglBastTokmat', row.NIK, $event)"
          />
        </template>
        <template #row-TglMhnByrTokmat="{ row }">
          <XInput
            v-model:value="row.NoMhnByrTokmat"
            @change="Update('NoMhnByrTokmat', row.NIK, $event)"
          />
          <DatePicker
            v-model:value="row.TglMhnByrTokmat"
            @change="Update('TglMhnByrTokmat', row.NIK, $event)"
          />
        </template>
        <template #row-TglBaByrTokmat="{ row }">
          <XInput
            v-model:value="row.NoBaByrTokmat"
            @change="Update('NoBaByrTokmat', row.NIK, $event)"
          />
          <DatePicker
            v-model:value="row.TglBaByrTokmat"
            @change="Update('TglBaByrTokmat', row.NIK, $event)"
          />
        </template>
        <template #row-TglSpkRuspin="{ row }">
          <XInput
            v-model:value="row.NoSpkRuspin"
            @change="Update('NoSpkRuspin', row.NIK, $event)"
          />
          <DatePicker
            v-model:value="row.TglSpkRuspin"
            @change="Update('TglSpkRuspin', row.NIK, $event)"
          />
        </template>
        <template #row-TglDoRuspin="{ row }">
          <XInput
            v-model:value="row.NoDoRuspin"
            @change="Update('NoDoRuspin', row.NIK, $event)"
          />
          <DatePicker
            v-model:value="row.TglDoRuspin"
            @change="Update('TglDoRuspin', row.NIK, $event)"
          />
        </template>
        <template #row-TglSppRuspin="{ row }">
          <XInput
            v-model:value="row.NoSppRuspin"
            @change="Update('NoSppRuspin', row.NIK, $event)"
          />
          <DatePicker
            v-model:value="row.TglSppRuspin"
            @change="Update('TglSppRuspin', row.NIK, $event)"
          />
        </template>
        <template #row-TglBapRuspin="{ row }">
          <XInput
            v-model:value="row.NoBapRuspin"
            @change="Update('NoBapRuspin', row.NIK, $event)"
          />
          <DatePicker
            v-model:value="row.TglBapRuspin"
            @change="Update('TglBapRuspin', row.NIK, $event)"
          />
        </template>
        <template #row-TglBastRuspin="{ row }">
          <XInput
            v-model:value="row.NoBastRuspin"
            @change="Update('NoBastRuspin', row.NIK, $event)"
          />
          <DatePicker
            v-model:value="row.TglBastRuspin"
            @change="Update('TglBastRuspin', row.NIK, $event)"
          />
        </template>
        <template #row-TglMhnByrRuspin="{ row }">
          <XInput
            v-model:value="row.NoMhnByrRuspin"
            @change="Update('NoMhnByrRuspin', row.NIK, $event)"
          />
          <DatePicker
            v-model:value="row.TglMhnByrRuspin"
            @change="Update('TglMhnByrRuspin', row.NIK, $event)"
          />
        </template>
        <template #row-TglBaByrRuspin="{ row }">
          <XInput
            v-model:value="row.NoBaByrRuspin"
            @change="Update('NoBaByrRuspin', row.NIK, $event)"
          />
          <DatePicker
            v-model:value="row.TglBaByrRuspin"
            @change="Update('TglBaByrRuspin', row.NIK, $event)"
          />
        </template>
      </Grid>
      <ValidasiDetail
        v-model:show="showDetailModal"
        :noRef="selectedRef"
        :area="area"
      />
      <ProposalDetail v-model:nik="selectedNIK" :show="showProposalModal" />
      <ChecklistPencairan
        :kodedagri="kodedagri"
        :tahun="tahun"
        v-model:show="showChecklistModal"
      />
      <ChecklistPencairan23
        :nik="selectedNIK"
        :kodedagri="kodedagri"
        :termin="termin"
        :tahun="tahun"
        v-model:show="showChecklist23Modal"
      />
      <DataUmum
        :tahun="tahun"
        :kodeDagri="kodedagri"
        v-model:show="showDataUmum"
      />
      <RAB v-model:show="showRAB" :kodeDagri="kodedagri" :tahun="tahun" />
    </div>
  </Page>
</template>
<script>
import Sidebar from '../InputUsulan/SideBar.vue'
import DesaGlance from '../DesaGlance.vue'
import ValidasiDetail from '../ValidasiData/ValidasiDetail.vue'
import ProposalDetail from '../InputUsulan/ProposalDetail.vue'
import ChecklistPencairan from './Checklist.vue'
import ChecklistPencairan23 from './Checklist23.vue'
import DataUmum from './DataUmum.vue'
import RAB from '../InputUsulan/RAB.vue'

export default {
  components: {
    Sidebar,
    DesaGlance,
    ValidasiDetail,
    ProposalDetail,
    ChecklistPencairan,
    ChecklistPencairan23,
    DataUmum,
    RAB,
  },
  data: () => ({
    priorcolors: ['#333', '#e53935', '#F4511E', '#43A047', '#1E88E5'],
    datagrid: [],
    area: {},
    showDetailModal: false,
    showProposalModal: false,
    showChecklistModal: false,
    showChecklist23Modal: false,
    showRAB: false,
    termin: 2,
    showDataUmum: false,
    selectedRef: null,
    selectedNIK: null,
    kodedagri: null,
    tahun: null,
    doPrint: 0,
    doRebind: 0,
    panitiaIcon: {
      Ketua: 'mdi-crown-circle-outline',
      Sekretaris: 'mdi-pencil-circle-outline',
      Anggota: 'mdi-account-circle-outline',
    },
    rebindSidebar: 0,
    filterArea: {
      IsApproved: 1,
    },
    rowMode: 'standard',
    IsRekom: false,
    NoRefs: ',',
  }),
  computed: {
    dbref() {
      // if (this.area.tabId == 2) {
      //   return 'PRM.ProposalDet'
      // } else {
      //   return 'PRM.ProposalBSPS'
      // }
      return 'RLK.ProposalDet'
    },
    areaParams() {
      let { Kabupaten, Kecamatan, Kelurahan } = this.area
      return { Kabupaten, Kecamatan, Kelurahan }
    },
    jmlUsulan() {
      return this.datagrid.filter((d) => {
        return d.CheckedValue
      }).length
    },
    buttonStatus() {
      return this.datagrid.reduce((total, curr) => {
        if (curr.IsSelected && curr.Tahapan) total['IsCancel'] = true
        if (curr.IsSelected && !curr.Tahapan) total['IsRekom'] = true
        return total
      }, {})
    },
  },
  watch: {
    showDetailModal(val) {
      if (!val) this.selectedRef = null
    },
    datagrid(val) {
      this.IsRekom = false
      this.NoRefs = ','
      if (val && val.length) {
        val.forEach((v) => {
          if (v.IsApproved == 2) this.IsRekom = true
          if (v.IsSelected) this.NoRefs += v.NoRef + ','
        })
      }
    },
  },
  methods: {
    ClickTambahBaru() {
      this.showDetailModal = true
    },
    OpenDetail(noRef) {
      this.selectedRef = noRef
      this.showDetailModal = true
    },
    OpenProposal(nik) {
      this.selectedNIK = nik
      this.showProposalModal = true
    },
    OpenChecklist() {
      this.kodedagri = this.area.KodeDagri
      this.tahun = this.area.Tahun
      this.showChecklistModal = true
    },
    OpenChecklist23(nik, termin) {
      this.selectedNIK = nik
      this.kodedagri = this.area.KodeDagri
      this.termin = termin
      this.tahun = this.area.Tahun
      this.showChecklist23Modal = true
    },
    OpenDataUmum() {
      this.kodedagri = this.area.KodeDagri
      this.tahun = this.area.Tahun
      this.showDataUmum = true
    },
    OpenRAB() {
      this.kodedagri = this.area.KodeDagri
      this.tahun = this.area.Tahun
      this.showRAB = true
    },
    filterGrid(row) {
      return Boolean(row.IsApproved)
    },
    async ChangeRole(txt, row) {
      let ret = await this.Update('Panitia', row.NIK, txt == '-' ? null : txt)
      if (ret.success) row.Panitia = txt == '-' ? null : txt
    },
    async Update(col, nik, val) {
      // console.log(col, nik, val)
      return await this.$api.call('RLK_UpdProposalDet', {
        Column: col,
        Value: val,
        NIK: nik,
      })
    },
    Download() {
      window.open(
        this.$api.url +
          '/report/backlog/doc/RPD.ods?out=xlsx&Tahun=' +
          this.area.Tahun +
          '&KelurahanID=' +
          this.area.KelurahanID
      )
    },
    async Rekom(rekom) {
      let ret = await this.$api.call('RLK.SavProposalRekom', {
        ProposalID: this.area.ProposalID,
        IsApproved: rekom,
        NoDPA: this.NoRefs,
      })
      // this.dbparams.isRekom = { ...this.dbparams, isRekom: rekom };
      if (ret.success) {
        this.rebindSidebar++
        this.IsRekom = rekom
      }
    },
  },
}
</script>
<style lang="scss">
.page-pencairan-backlog {
  #tbl-grid {
    max-width: 100%;
    width: 1126px;
  }
}
</style>
