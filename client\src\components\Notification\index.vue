<template>
  <div class="notification-wrapper">
    <v-btn v-if="!isSubscribed" @click="subscribeToNotifications">
      Enable Notifications
    </v-btn>

    <div v-else class="notification-settings">
      <v-icon color="success">mdi-bell-check</v-icon>
      <span>Notifications enabled</span>

      <v-btn size="small" @click="unsubscribeFromNotifications" class="ml-4">
        Disable
      </v-btn>
    </div>
  </div>
</template>

<script>
export default {
  data: () => ({
    isSubscribed: false,
    registration: null,
    subscription: null,
  }),

  async mounted() {
    if ('serviceWorker' in navigator && 'PushManager' in window) {
      try {
        this.registration = await navigator.serviceWorker.ready
        this.subscription =
          await this.registration.pushManager.getSubscription()
        this.isSubscribed = !!this.subscription
      } catch (error) {
        console.error('Error checking subscription:', error)
      }
    }
  },

  methods: {
    async subscribeToNotifications() {
      if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
        alert('Push notifications are not supported in your browser')
        return
      }

      try {
        const permission = await Notification.requestPermission()
        if (permission !== 'granted') {
          throw new Error('Permission not granted for notifications')
        }

        const subscription = await this.registration.pushManager.subscribe({
          userVisibleOnly: true,
          applicationServerKey: this.urlBase64ToUint8Array(
            'BHNlq0xbaervKOcZHEFR8lNa8Z1F72uB5lsbZf9NyLtI7AGF2d9nwNPiFSrNqkwDneL8UpBsZKcJ7pQeNKTXUE0'
          ),
        })

        // Send subscription to server
        try {
          await this.$http.post('/api/notifications/subscribe', {
            subscription,
            userId: this.$store.state.user.UserID, // Get user ID from Vuex store
          })
          this.subscription = subscription
          this.isSubscribed = true
        } catch (error) {
          console.error('Error saving subscription:', error)
          await subscription.unsubscribe()
          throw error
        }
      } catch (error) {
        console.error('Error subscribing to push notifications:', error)
        alert('Failed to enable notifications: ' + error.message)
      }
    },

    async unsubscribeFromNotifications() {
      try {
        await this.subscription.unsubscribe()
        // TODO: Notify server about unsubscription
        this.subscription = null
        this.isSubscribed = false
      } catch (error) {
        console.error('Error unsubscribing:', error)
      }
    },

    urlBase64ToUint8Array(base64String) {
      const padding = '='.repeat((4 - (base64String.length % 4)) % 4)
      const base64 = (base64String + padding)
        .replace(/-/g, '+')
        .replace(/_/g, '/')

      const rawData = window.atob(base64)
      const outputArray = new Uint8Array(rawData.length)

      for (let i = 0; i < rawData.length; ++i) {
        outputArray[i] = rawData.charCodeAt(i)
      }
      return outputArray
    },
  },
}
</script>

<style scoped>
.notification-wrapper {
  display: flex;
  align-items: center;
}

.notification-settings {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
