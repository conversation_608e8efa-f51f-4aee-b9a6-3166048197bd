const axios = require("axios");
var db = require("../../common/db");

const url = function(kdwilayah) {
  return `http://maspetruk.dpubinmarcipka.jatengprov.go.id/manajemen_master/pekerjaan/list_json/${kdwilayah}?depan=true`;
};

const RABMAP = {
  'Bata Ringan Tebal 10 cm': {
    KodeRAB: 'A.1',
    <PERSON><PERSON>: 'bata_ringan'
  },
  'Semen Merah': {
    KodeRAB: 'A.2',
    <PERSON><PERSON>: 'bata_ringan'
  },
  'Baja ringan canai dingin C75':  {},
  
}

const maspetruk = {
  async read(kdwilayah) {
    let d = await axios
      .get(url(kdwilayah))
      .catch((err) => {
        console.log(err);
      });
    return d ? d.data : [];
  },
  async delete(nik, kode_wilayah) {
    await axios
      .post(url("delete"), {
        nik: nik,
        kode_wilayah: kode_wilayah,
      })
      .catch((err) => {
        console.log(err);
      });
  },
  async import() {
    let kabs = await db.exec("Arch_SelArea", {ParentAreaID: 33});
    for(let k of kabs) {
      const j = await maspetruk.read(k.AreaID)
      console.log(j.data)
    }
    process.exit()
  },
};

maspetruk.import()