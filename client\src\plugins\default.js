// import Vue from 'vue' // Not needed in Vue 3
import {
  XInput,
  XSelect,
  DatePicker,
  Search,
  TextArea,
  Checkbox,
  List,
  XLabel,
  XMap, // Renamed import
  MenuButton,
} from '../components/Forms'
import Page from '../components/Page.vue'
import Grid from '../components/Grid/index.vue'
import Modal from '../components/Modal.vue'
import Uploader from '../components/Uploader/index.vue'
import Panel from '../components/Panel.vue'
import NikBlock from '../components/NikBlock.vue'

export default {
  register(app) {
    // Accept the app instance
    app.component('XInput', XInput)
    app.component('XSelect', XSelect)
    //eslint.vuejs.org/rules/multi-word-component-names.html
    app.component('Search', Search)
    app.component('DatePicker', DatePicker)
    app.component('TextArea', TextArea)
    app.component('Checkbox', Checkbox)
    app.component('Page', Page)
    app.component('Grid', Grid)
    app.component('Modal', Modal)
    app.component('List', List)
    app.component('XLabel', XLabel)
    app.component('Uploader', Uploader)
    app.component('XMap', XMap) // Renamed component registration
    app.component('MenuButton', MenuButton)
    app.component('Panel', Panel)
    app.component('NikBlock', NikBlock)
  },
}
