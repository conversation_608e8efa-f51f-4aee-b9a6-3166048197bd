<template>
  <Modal title="Rekening Bank" v-model:show="x_show" @onSubmit="Submit">
    <XInput
      type="text"
      label="No Rekening"
      v-model:value="form.NoRekening"
      width="300px"
    />
    <XInput
      type="text"
      label="Nama Rekening"
      v-model:value="form.NamaRekening"
      width="300px"
    />
    <XInput
      type="text"
      label="Cabang Pembuka"
      v-model:value="form.NamaBank"
      width="300px"
    />
  </Modal>
</template>
<script>
export default {
  data: () => ({
    x_show: false,
    form: {},
  }),
  props: {
    nobdt: String,
    show: <PERSON>olean,
  },
  watch: {
    nobdt() {
      this.form.NamaBank = ''
      this.form.NamaRekening = ''
      this.form.NoRekening = ''
    },
    show(val) {
      this.x_show = val
      if (val) this.Populate()
    },
    x_show() {
      this.$emit('update:show', this.x_show)
    },
  },
  methods: {
    async Populate() {
      this.form.NoBDT = this.nobdt
      let d = await this.$api.call('PRM.SelRekeningBansos', this.form)
      if (d.data.length) {
        this.form = { ...d.data[0] }
      }
    },
    async Submit() {
      this.form.NoBDT = this.nobdt
      let ret = await this.$api.call('PRM.SavRekeningBansos', this.form)
      if (ret.success) {
        this.$emit('success')
        this.$emit('update:show', false)
      }
    },
  },
}
</script>
<style lang="scss"></style>
