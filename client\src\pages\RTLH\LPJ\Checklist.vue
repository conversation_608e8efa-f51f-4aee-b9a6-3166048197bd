<template>
  <Modal
    title="CHECKLIST LPJ"
    v-model:show="xshow"
    width="400px"
    @onSubmit="Save"
  >
    <div style="display: flex" class="form-inline">
      <div class="iblock" style="margin-right: 10px">
        <div
          style="
            padding: 8px 0;
            background: #f3f3f3;
            width: 100%;
            display: flex;
          "
        >
          <div
            class="iblock"
            style="flex: 1; padding-left: 10px; font-size: small"
          >
            <i class="fa fa-file"></i>BERKAS LPJ
          </div>
        </div>
        <div style="display: flex">
          <Uploader
            :disabled="true"
            v-model:value="forms.LPJ"
            accept=".pdf"
          ></Uploader>
          <div style="padding: 0 8px">
            <Checkbox
              text="Laporan Pelaksanaan"
              v-model:value="forms.LapPelaksanaan"
            />
            <Checkbox
              text="Surat Pernyataan Tanggung Jawab"
              v-model:value="forms.SuratTggJwb"
            />
            <Checkbox
              text="Laporan Realisasi Penggunaan Dana"
              v-model:value="forms.RealisasiDana"
            />
            <Checkbox
              text="Bukti Pen<PERSON>ua<PERSON>"
              v-model:value="forms.BuktiUangKeluar"
            />
            <Checkbox
              text="Bukti Setoran Pajak"
              v-model:value="forms.BuktiPajak"
            />
            <Checkbox
              text="Dokumentasi Kegiatan"
              v-model:value="forms.FotoKegiatan"
            />
          </div>
        </div>
        <div
          style="
            padding: 8px 0;
            background: #f3f3f3;
            width: 100%;
            display: flex;
          "
        >
          <div
            class="iblock"
            style="flex: 1; padding-left: 10px; font-size: small"
          >
            <i class="fa fa-file"></i>BERKAS PADAT KARYA
          </div>
        </div>
        <div style="display: flex">
          <Uploader
            :disabled="true"
            v-model:value="forms.PadatKarya"
            accept=".pdf"
          ></Uploader>
          <div style="padding: 0 8px">
            <Checkbox
              text="Laporan Pelaksanaan"
              v-model:value="forms.Pengantar"
            />
            <Checkbox
              text="Dokumentasi Pelaksanaan"
              v-model:value="forms.FCRekDesa"
            />
            <Checkbox
              text="Dok. Makan dan Minum"
              style="width: 300px"
              v-model:value="forms.DaftarHadir"
            />
            <!-- <Checkbox text="" v-model:value="forms.LapTPK" /> -->
            <Checkbox text="Kwitansi Upah" v-model:value="forms.BAPPencairan" />
          </div>
        </div>
      </div>
    </div>
    <template v-slot:left-action>
      <div style="padding-left: 17px; color: gray">
        <Checkbox text="Check Semua" v-model:value="checkAll" />
      </div>
    </template>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    xshow: false,
    forms: {},
    checkAll: false,
  }),
  props: {
    show: Boolean,
    nik: [String, Number],
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      this.$emit('update:show', val)
    },
    nik(val) {
      if (val) this.populate()
    },
    checkAll(val) {
      for (let x in this.forms) {
        if (x !== 'NIK') this.forms[x] = val
      }
    },
  },
  methods: {
    async populate() {
      let { data } = await this.$api.call('PRM.SelMonevCheck', {
        NIK: this.nik,
      })
      this.forms = data[0]
    },
    async Save() {
      let ret = await this.$api.call('PRM.SavMonevCheck', {
        ...this.forms,
        NIK: this.nik,
      })
      if (ret.success) this.$emit('update:show', false)
    },
  },
}
</script>
<style lang="scss">
.modal-proposal {
  .v-dialog--content {
    padding: 0;
  }
}
</style>
