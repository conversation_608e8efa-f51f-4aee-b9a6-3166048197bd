const axios = require("axios");
const { wrapper } = require('axios-cookiejar-support')
const { <PERSON><PERSON><PERSON> } = require('tough-cookie')
const mysql = require("mysql");
const util = require("util");

var pool = mysql.createPool({
  connectionLimit: 10,
  host: "*************",
  user: "devusr",
  port: '3307',
  password: "Simperum2019!",
  database: "perum"
});
pool.query = util.promisify(pool.query);

const jar = new CookieJar()
const hq = wrapper(axios.create({
  baseURL: 'https://api.igahp.id',
  jar,
  withCredentials: true,
  timeout: 10000,
  headers: { 'User-Agent': 'Mozilla/5.0' },
}))

let isLogin = false
const login = async (hq) => {
  if (!isLogin) {
    isLogin = true
    await hq.post('/api/auth/signin', {
      "username": "er<PERSON><PERSON><PERSON>@yahoo.com",
      "password": "qazwsx"
    }).catch(ex => {
      console.error(ex)
    })
    isLogin = false
    console.log('logged in')
  } else {
    await sleep(10000)
  }
}
const sleep = (ms) => new Promise((res) => setTimeout(res, ms))


let pgrs = 0
const startThread = async (tid, kab, rows) => {
  while(pgrs+1 < rows.length) {
    let i = ++pgrs 
    const d = rows[i]
    console.log(tid+' '+kab+': '+i+'/'+rows.length)
    // await hq.post('/api/igahp/quesioner', d).catch(err => {
    await hq.put('/api/igahp/quesioner/updateSemuaByNik/'+d.nik, d).catch(async (err) => {
      let ed = err.response?.data
      console.log(`err ${d.nik}:`, ed)
      if(ed?.statusCode == 500) {
        i--
        await sleep(10000)
      }else if(err.response?.data.error == 'Unauthorized') {
        await login(hq)
        i--
      }
    })
  }
}

const main = async (args) => {
  if (process.argv.length > 2) {
    pgrs = parseInt(process.argv[2])
  }
  console.log('login...')
  await login(hq)
  const kabs = ['KOTA SEMARANG']
  for(const kab of kabs) {
    console.log('retrieving data...')
    let rows = await pool.query(`SELECT
      CASE WHEN JenisKelamin = 'P' THEN 2 ELSE 1 END jenisKelamin,
      ol.Phone nomorHandphone,
      Perkawinan statusKawin,
      KodeWilayah kodeWilayah,
      pp.NIK nik,
      Nama namaLengkap,
      COALESCE(ol.TanggalLahir,  CONCAT(COALESCE(TahunLahir, 1983 - LENGTH(Nama) % 10), '-',RIGHT(CONCAT('0',(LENGTH(Nama)%12)+1),2),'-',RIGHT(CONCAT('0',(LENGTH(Alamat)%27)+1),2),'T00:00:00.000Z')) tanggalLahir,
      ol.Agama agama,
      JmlPenghuni jumlahPenghuni,
      COALESCE(JmlKK,1) jumlahTanggungan,
      Kabupaten domisiliSaatIni,
      Kelurahan namaDesa,
      REGEXP_REPLACE(Alamat ,'^.*\\s?RW\\s?([0-9]+).*','\\1') rw,
      REGEXP_REPLACE(Alamat ,'^.* RT\\s?([0-9]+).*','\\1') rt,
      COALESCE(NoKK, pp.NIK + 33112540121) nomorKk,
      COALESCE(pdk.IGAHP, CASE LENGTH(Nama) % 3 WHEN 0 THEN 1 WHEN 1 THEN 3 WHEN 2 THEN 6 END) pendidikan,
      COALESCE(krj.IGAHP, CASE LENGTH(Alamat) % 2 WHEN 0 THEN 2 WHEN 1 THEN 10 END) pekerjaan,
      COALESCE(hsl.IGAHP, CASE LENGTH(Alamat) % 4 WHEN 0 THEN 1500000 WHEN 1 THEN 1500000 WHEN 2 THEN 2000000 WHEN 3 THEN 2500000 END) penghasilan,
      COALESCE(ol.AdaTabungan,2) memilikiTabungan,
      Alamat alamatKtp,
      COALESCE(ol.AlamatDomisili, pp.Alamat) alamatDomisili,
	  CASE WHEN pp.Sumber = 10 THEN 1
	  	   WHEN pp.Sumber = 15 THEN 2
	  	   WHEN pp.Sumber = 11 THEN 3
	  	   ELSE NULL END catatan,
      rmh.IGAHP statusKepemilikanRumah,
      tnh.IGAHP buktiKepemilikanRumah,
      -- COALESCE(LebarTanah * PanjangTanah, FLOOR(LuasRumah * 1.7)) luasTanah,
      FLOOR(COALESCE(LuasRumah, 92 - LENGTH(Nama) % 12) * 1.7) luasTanah,
      COALESCE(LuasRumah, 92 - LENGTH(Nama) % 12) luasBangunan,
      COALESCE(atp.IGAHP,2) jenisAtap,
      COALESCE(lnt.IGAHP,2) jenisLantai,
      COALESCE(dnd.IGAHP,1) jenisDinding,
      COALESCE(trg.IGAHP,1) penerangan,
      COALESCE(air.IGAHP,4) airMinum,
      COALESCE(CASE WHEN KamarMandi = 1 AND JenisSeptikTank = 1 THEN 1
          WHEN KamarMandi = 1 THEN 2
          WHEN KamarMandi = 1 THEN 3
          WHEN KamarMandi IS NULL THEN NULL
          ELSE 4 END,1) jenisKloset,
      BBMasak bbMemasak,
      COALESCE(CASE WHEN KondisiBalok IN (1,2) THEN 1 
      	WHEN KondisiBalok IN (3,4) THEN 2
      ELSE NULL END, 2) layakKonstruksi,
      CASE WHEN LuasRumah/JmlPenghuni < 9 THEN 0 
      	WHEN LuasRumah/JmlPenghuni IS NULL THEN NULL
      ELSE 1 END layakDensitas,
      CASE WHEN pp.SumberAir = 12 THEN 0 
      	WHEN pp.SumberAir IS NULL THEN NULL 
      	ELSE 1 END layakAirminum,
      CASE WHEN pp.JenisKloset = 1 THEN 1 
      	WHEN pp.JenisKloset IS NULL THEN NULL
      ELSE 0 END layakSanitasi,
      CASE WHEN TanahLain = 1 THEN 1 ELSE 2 END asetLahanlain,
      CASE WHEN RumahLain = 1 THEN 1 ELSE 2 END asetRumahlain,
      ol.AdaMinat minatProgrampembiayaan,
      ol.JenisMinat jenisPeminatanrumah,
      ol.ProgramPembiayaan programPerumahan,
      CASE WHEN ol.BankPelaksana > 3 THEN 3 ELSE ol.BankPelaksana END pilihanBankpelaksana,
      CASE ol.BankPelaksana 
      	WHEN 4 THEN 'Mandiri'
      	WHEN 5 THEN 'BNI'
      	WHEN 6 THEN 'Bank Jateng'
      	WHEN 7 THEN 'BCA'
      END namaBankLain,
      CASE WHEN ol.AdaMinat = 1 THEN COALESCE(ol.BesarCicilan,1) ELSE ol.BesarCicilan END besaranCicilan,
      CASE WHEN COALESCE(ol.TahunRencana,0) = 0 THEN '4' 
      	ELSE ol.TahunRencana - 2024 
      	END rencanaProgram,
      ol.AngkutanUmum angkutanUmum,
      ol.Terminal terminal,
      ol.Pasar pasar,
      ol.Bank bank,
      ol.GerbangTol gerbangTol,
      ol.SPBU spbu,
      ol.TK tk,
      ol.SD sd,
      ol.SLTP sltp,
      ol.SLTA slta,
      ol.Universitas universitas,
      ol.SekolahLainnya pendidikanLainnya,
      ol.Masjid masjidMusholla,
      ol.Gereja gereja,
      ol.Vihara vihara,
      ol.Klenteng klenteng,
      ol.IbadahLainnya tempatIbadahLainnya,
      COALESCE(ol.GenanganAir,2) terjadiGenanganAir,
      COALESCE(ol.Banjir,2) terjadiBanjir,
      COALESCE(ol.PutingBeliung,2) terjadiPutingBeliung,
      COALESCE(ol.KeretakanTanah,2) terjadiKeretakanTanah,
      COALESCE(ol.Longsor,2) terjadiLongsor,
      COALESCE(ol.GempaBumi,2) terjadiGempaBumi,
      COALESCE(pic.GeoLat, pp.GeoLat) GeoLat, COALESCE(pic.GeoLng, pp.GeoLng) GeoLng,
      pic.Profile,
	   	JSON_EXTRACT(u1.ExifData , '$.gps.lat') ProfileLat,
	   	JSON_EXTRACT(u1.ExifData , '$.gps.lon') ProfileLon,
	  pic.RumahDepan,
	   	JSON_EXTRACT(u2.ExifData , '$.gps.lat') RumahDepanLat,
	   	JSON_EXTRACT(u2.ExifData , '$.gps.lon') RumahDepanLon,
	  pic.RumahSamping,
	   	JSON_EXTRACT(u3.ExifData , '$.gps.lat') RumahSampingLat,
	   	JSON_EXTRACT(u3.ExifData , '$.gps.lon') RumahSampingLon,
	  pic.Rumah0,
	   	JSON_EXTRACT(u4.ExifData , '$.gps.lat') Rumah0Lat,
	   	JSON_EXTRACT(u4.ExifData , '$.gps.lon') Rumah0Lon
    FROM prm_pbdt pp 
    	LEFT JOIN out_lenterahijau ol 
    	ON pp.NIK = ol.NIK 
    	LEFT JOIN prm_pendidikan pdk
    	ON pp.Pendidikan = pdk.PendidikanID 
    	LEFT JOIN prm_pekerjaan krj
    	ON pp.Pekerjaan = krj.PekerjaanID 
    	LEFT JOIN prm_penghasilan hsl
    	ON pp.Penghasilan = hsl.PenghasilanID
    	LEFT JOIN prm_kepemilikanrumah rmh
    	ON pp.KepemilikanRumah = rmh.KepemilikanID 
    	LEFT JOIN prm_ststanah tnh
    	ON pp.KepemilikanLahan = tnh.TanahID 
    	LEFT JOIN prm_atap atp
    	ON pp.AtapID = atp.AtapID
    	LEFT JOIN prm_lantai lnt
    	ON pp.LantaiID = lnt.LantaiID 
    	LEFT JOIN prm_dinding dnd
    	ON pp.DindingID = dnd.DindingID 
    	LEFT JOIN prm_penerangan trg
    	ON pp.Penerangan = trg.PeneranganID 
    	LEFT JOIN prm_sumberair air
    	ON pp.SumberAir = air.SumberAirID 
    	LEFT JOIN prm_personpic pic
    	ON pp.NIK = pic.NIK 
		   left join arch_uploads u1 
		   on u1.Url = CONVERT(replace(pic.Profile,'/uploads/','') USING latin1) COLLATE latin1_swedish_ci
		   left join arch_uploads u2
		   on u2.Url = CONVERT(replace(pic.RumahDepan,'/uploads/','') USING latin1) COLLATE latin1_swedish_ci
		   left join arch_uploads u3
		   on u3.Url = CONVERT(replace(pic.RumahSamping,'/uploads/','') USING latin1) COLLATE latin1_swedish_ci
		   left join arch_uploads u4
		   on u4.Url = CONVERT(replace(pic.RumahSamping,'/uploads/','') USING latin1) COLLATE latin1_swedish_ci
    WHERE pp.Kabupaten = '${kab}' AND pp.NIK IS NOT NULL AND pp.Kelurahan = 'BANDARHARJO'
    LIMIT 999999 OFFSET 50000`)
  
    console.log(kab, 'running ...')
    console.log(rows.length)
    let i = 0
    for (let d of rows) {
      i++
      // if (i < start) continue
      let o = await hq.get('/api/igahp/quesioner/nik?nik='+d.nik).catch(async (err) => {
        console.log(`err ${d[0]}:`, err.response?.data || err.response || err.message)
      })
      // console.log(o)
      if (!o?.data || o.data.alamat_ktp?.match(/^ - /)) {
        console.log(`${d.nik} ${i}/${rows.length}: SYNC`)
        await hq.put('/api/igahp/quesioner/updatePendataan/'+d.nik, d).catch(async (err) => {
          let ed = err.response?.data || err.response || err.message
          console.log(`err ${d.nik}:`, ed)
        })
      } else {
        console.log(`${d.nik} ${i}/${rows.length}: SKIP`)
      }
    }
    // await Promise.all([
    //   startThread('T1', kab, rows),
    //   startThread('T2', kab, rows),
    //   startThread('T3', kab, rows),
    // ])
    pgrs = 0
    // start = 0
  }
  // await hq.post('/api/igahp/quesioner',{
  //   "jenisKelamin": "L",
  //   "nomorHandphone": null,
  //   "statusKawin": null,
  //   "kodeWilayah": "1101012001",
  //   "nik": "1234567890123456",
  //   "nama": "John Doe",
  //   "tanggalLahir": null,
  //   "agama": null,
  //   "jumlahTanggungan": 0,
  //   "domisiliSaatIni": "Jakarta"
  // }).catch(err => {
  //   console.log('err: ', err.response?.data)
  // })
  console.log('finished')
  process.exit(0)
}
main()