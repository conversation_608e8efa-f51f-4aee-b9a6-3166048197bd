<template>
  <Modal title="DATA UMUM" v-model:show="xshow" width="500px" @onSubmit="Save">
    <div class="form-inline" style="width: 500px">
      <component
        v-for="(c, idx) in inputs"
        :key="idx"
        :is="c.coms ? c.coms : 'XInput'"
        :type="c.type ? c.type : 'text'"
        :label="c.label ? c.label : c"
        v-model:value="forms[c.value || c.replace(/\s/g, '')]"
      ></component>
    </div>
  </Modal>
</template>
<script>
import TextArea from '../../../components/Forms/TextArea.vue'
import XInput from '../../../components/Forms/XInput.vue'
export default {
  components: {
    XInput,
    TextArea,
  },
  data: () => ({
    xshow: false,
    inputs: [
      'Nama Ruspin',
      'Direktur Ruspin',
      { coms: 'TextArea', label: '<PERSON>ama<PERSON> Ruspin', value: 'AlamatRuspin' },
      'NPWP Ruspin',
      'NoRek Ruspin',
      {
        coms: 'XInput',
        type: 'number',
        label: '<PERSON><PERSON>uspin',
        value: '<PERSON><PERSON>Ruspin',
      },
      'Nama Tokmat',
      'Direktur Tokmat',
      { coms: 'TextArea', label: 'Alamat Tokmat', value: 'AlamatTokmat' },
      'NPWP Tokmat',
      'NoRek Tokmat',
      {
        coms: 'XInput',
        type: 'number',
        label: 'Nilai Tokmat',
        value: 'NilaiTokmat',
      },
      'Nama Pokmas',
      'Ketua Pokmas',
      'No VA',
    ],
    forms: {},
  }),
  props: {
    show: Boolean,
    kodeDagri: [String, Number],
    tahun: [String, Number],
  },
  watch: {
    show(val) {
      this.xshow = val
      if (val) this.Populate()
      else this.Reset()
    },
    xshow(val) {
      this.$emit('update:show', val)
    },
  },
  methods: {
    Reset() {
      for (let c of this.inputs) {
        this.forms[c.value || c.replace(/\s/g, '')] = null
      }
    },
    async Populate() {
      let d = await this.$api.call('RLK_SelDataUmum', {
        KodeDagri: this.kodeDagri,
        Tahun: this.tahun,
      })
      if (d.data && d.data.length) this.forms = d.data[0]
    },
    async Save() {
      let ret = await this.$api.call('RLK_SavDataUmum', {
        KodeDagri: this.kodeDagri,
        Tahun: this.tahun,
        ...this.forms,
      })
      if (ret.success) this.$emit('update:show', false)
    },
  },
}
</script>
<style lang="scss">
.modal-data-umum {
  .--input,
  textarea {
    width: 300px !important;
  }
}
</style>
