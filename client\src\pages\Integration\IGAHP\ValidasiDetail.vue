<template>
  <Modal
    v-model:show="x_show"
    title="VALIDASI DETAIL"
    :loading="loading"
    :error="error"
    :error-action="errorAction"
    :warning="warning"
    :show-actions="!disabled"
    :saving="saving"
    @on-submit="Save"
    @error-action-yes="errorActionYes"
    @error-action-no="errorActionNo"
  >
    <template #left-action>
      <v-btn v-if="AllowDelete" variant="text" color="red" @click="Delete">Hapus</v-btn>
    </template>
    <div
      style="display: flex"
      :style="isMobile ? 'width:100vw;' : 'width:680px; height:610px;'"
      :class="isMobile ? '' : 'form-inline'"
    >
      <div v-show="!isMobile" style="width: 50px; color: #333">
        <div
          class="vert-tab"
          :class="{
            active: tabId == 'Individu',
          }"
          @click="tabId = 'Individu'"
        >
          <v-icon style="font-size: 36px">mdi-account</v-icon>
        </div>
        <div
          tab="Rumah"
          class="vert-tab"
          :class="{
            active: tabId == 'Rumah',
          }"
          @click="tabId = 'Rumah'"
        >
          <v-icon style="font-size: 36px">mdi-home</v-icon>
        </div>
        <div
          tab="Konstruksi"
          class="vert-tab"
          :class="{
            active: tabId == 'Konstruksi',
          }"
          @click="tabId = 'Konstruksi'"
        >
          <v-icon style="font-size: 36px">mdi-hammer-wrench</v-icon>
        </div>
        <div
          tab="Kesehatan"
          class="vert-tab"
          :class="{
            active: tabId == 'Kesehatan',
          }"
          @click="tabId = 'Kesehatan'"
        >
          <v-icon style="font-size: 36px">mdi-stethoscope</v-icon>
        </div>
        <div
          tab="Foto"
          class="vert-tab"
          :class="{
            active: tabId == 'Foto',
          }"
          @click="tabId = 'Foto'"
        >
          <v-icon style="font-size: 36px">mdi-image-multiple</v-icon>
        </div>
        <!-- <div
          tab="FAQ"
          class="tab"
          @click="tabId = 'FAQ'"
          :class="{
            active: tabId == 'FAQ',
          }"
        >
          <v-icon style="font-size: 36px">mdi-account-question</v-icon>
        </div> -->
        <div
          v-if="igahp"
          tab="Lentera"
          class="vert-tab"
          :class="{
            active: tabId == 'Lentera',
          }"
          @click="tabId = 'Lentera'"
        >
          <!-- <v-icon style="font-size: 36px">mdi-coach-lamp</v-icon> -->
          <img
            src="/imgs/igahp.svg"
            height="32px"
            style="margin-top: 10px; filter: grayscale(100%); opacity: 0.5"
          />
        </div>
      </div>
      <div
        style="overflow: auto; border-left: 1px solid silver; margin-left: 8px"
        :style="
          isMobile ? 'width:calc(100vw - 40px);' : 'width:680px; height:100%;'
        "
      >
        <div v-show="tabId == 'Individu' || isMobile">
          <div style="padding: 10px" :class="isMobile ? '' : 'form-inline'">
            <XInput v-model:value="forms.NIK" label="NIK*">
              <template #label>
                <div style="display: flex">
                  <div>NIK*</div>
                  <v-spacer />
                  <v-btn
                    x-small
                    :outlined="forms.TipeData < 200 || isBDT"
                    color="primary"
                    style="margin-top: 2px; margin-right: 8px"
                    @click="CheckDinsos"
                  >
                    {{ 200 > forms.TipeData || isBDT ? 'bdt' : 'check bdt' }}
                  </v-btn>
                </div>
              </template>
            </XInput>
            <XInput v-model:value="forms.Nama" label="Nama*" />
            <XInput v-model:value="forms.NoKK" label="No. KK*" />
            <XInput v-model:value="forms.Alamat" label="Alamat KTP*" />
            <XInput
              v-model:value="forms.AlamatDomisili"
              label="Alamat Domisili"
            />
            <XInput v-model:value="forms.Phone" label="No HP*" />
            <!-- <XInput label="Umur*" v-model:value="forms.Umur" /> -->
            <DatePicker
              v-model:value="forms.TanggalLahir"
              label="Tanggal Lahir*"
            />
            <XSelect
              v-model:value="forms.JenisKelamin"
              label="Jns. Kelamin*"
              :items="[
                { val: 'L', txt: 'Laki-Laki' },
                { val: 'P', txt: 'Perempuan' },
              ]"
            />
            <XSelect
              v-model:value="forms.Agama"
              label="Agama*"
              :items="[
                { val: '1', txt: 'Islam' },
                { val: '2', txt: 'Kristen' },
                { val: '3', txt: 'Katolik' },
                { val: '4', txt: 'Hindu' },
                { val: '5', txt: 'Budha' },
                { val: '6', txt: 'Konghucu' },
                { val: '7', txt: 'Lainnya' },
              ]"
            />
            <XSelect
              v-model:value="forms.Perkawinan"
              label="Sts. Perkawinan*"
              dbref="PRM.SelPerkawinan"
            />
            <XSelect
              v-model:value="forms.Pendidikan"
              label="Pendidikan*"
              dbref="PRM.SelPendidikan"
            />
            <XSelect
              v-model:value="forms.Pekerjaan"
              label="Pekerjaan*"
              dbref="PRM.SelPekerjaan"
            />
            <XInput v-model:value="forms.NoNPWP" label="No. NPWP" />
            <XSelect
              v-model:value="forms.Penghasilan"
              label="Penghasilan*"
              dbref="PRM.SelPenghasilan"
            />
            <Checkbox
              v-model:value="forms.AdaTabungan"
              label="Memiliki Tabungan"
            />
            <XSelect
              v-model:value="forms.MampuSwadaya"
              label="Mampu Swadaya*"
              :items="[
                { val: '1', txt: 'Mampu' },
                { val: '0', txt: 'Tidak Mampu' },
              ]"
            />
            <!-- <div class="info-red">
              *) Data harus diisi untuk bisa masuk ERTLH
            </div> -->
          </div>
        </div>
        <div v-show="tabId == 'Rumah' || isMobile">
          <div style="padding: 10px" :class="isMobile ? '' : 'form-inline'">
            <XSelect
              v-model:value="forms.KepemilikanRumah"
              label="Sts. Rumah*"
              :items="[
                { val: 1, txt: 'Milik Sendiri' },
                { val: 2, txt: 'Kontrak/Sewa' },
                { val: 3, txt: 'Bebas Sewa' },
                { val: 4, txt: 'Dinas' },
                { val: 6, txt: 'Menumpang' },
                { val: 5, txt: 'Lainnya' },
              ]"
            />
            <XSelect
              v-model:value="forms.KepemilikanLahan"
              label="Sts. Lahan*"
              dbref="PRM.SelStatusTanah"
            />
            <XSelect
              v-model:value="forms.TanahLain"
              label="Tanah Lain*"
              :items="[
                { val: '1', txt: 'Memiliki' },
                { val: '0', txt: 'Tidak Memiliki' },
              ]"
            />
            <XInput
              v-model:value="forms.AlamatTanahLain"
              :label="'Alamat Tanah Lain' + (forms.TanahLain == '1' ? '*' : '')"
            />
            <XSelect
              v-model:value="forms.RumahLain"
              label="Rumah Lain*"
              :items="[
                { val: '1', txt: 'Memiliki' },
                { val: '0', txt: 'Tidak Memiliki' },
              ]"
            />
            <XInput
              v-model:value="forms.LuasRumah"
              label="Luas Rumah*"
              type="number"
            />
            <XInput
              v-model:value="forms.JmlPenghuni"
              label="Jml. Penghuni*"
              type="number"
            />
            <XInput
              v-model:value="forms.JmlKK"
              label="Jml. Keluarga*"
              type="number"
            />
            <XSelect
              v-model:value="forms.KawasanPerumahan"
              label="Kawasan*"
              dbref="PRM.SelKawasan"
              height="120px"
            />
            <XLabel label="Kondisi Data">
              <XSelect
                v-model:value="forms.Sumber"
                dbref="PRM.SelSumber"
                :dbparams="{ Kecuali: '1,2,3,4,5,6,7,8,9,12,13,14' }"
                height="150px"
                width="250px !important"
                style="margin-right: 10px"
              />
              <XSelect
                v-show="false"
                v-model:value="forms.Tahun"
                :items="limaTahun"
                placeholder="Tahun"
                width="80px !important"
              />
            </XLabel>
            <!-- <div class="info-red">
              *) Data harus diisi untuk bisa masuk ERTLH
            </div> -->
          </div>
        </div>
        <div v-show="tabId == 'Konstruksi' || isMobile">
          <div style="padding: 10px" :class="isMobile ? '' : 'form-inline'">
            <XSelect
              v-model:value="forms.AdaPondasi"
              label="Pondasi*"
              dbref="PRM.SelKondisi"
            />
            <XSelect
              v-model:value="forms.KondisiKolom"
              label="Kondisi Kolom*"
              dbref="PRM.SelKondisi"
            />
            <XSelect
              v-model:value="forms.KondisiBalok"
              label="Kondisi Balok*"
              dbref="PRM.SelKondisi"
            />
            <XSelect
              v-model:value="forms.KondisiSloof"
              label="Kondisi Sloof"
              dbref="PRM.SelKondisi"
            />
            <XSelect
              v-model:value="forms.KondisiPlafon"
              label="Kondisi Plafon"
              dbref="PRM.SelKondisi"
            />
            <XSelect
              v-model:value="forms.KondisiRangka"
              label="Rangka Atap*"
              dbref="PRM.SelKondisi"
            />
            <XSelect
              v-model:value="forms.LantaiID"
              label="Bahan Lantai*"
              dbref="PRM.SelLantai"
            />
            <XSelect
              v-model:value="forms.KondisiLantai"
              label="Kondisi Lantai*"
              dbref="PRM.SelKondisi"
            />
            <XSelect
              v-model:value="forms.DindingID"
              label="Bahan Dinding*"
              dbref="PRM.SelDinding"
            />
            <XSelect
              v-model:value="forms.KondisiDinding"
              label="Kondisi Dinding*"
              dbref="PRM.SelKondisi"
            />
            <XSelect
              v-model:value="forms.AtapID"
              label="Bahan Atap*"
              dbref="PRM.SelAtap"
              height="120px"
            />
            <XSelect
              v-model:value="forms.KondisiAtap"
              label="Kondisi Atap*"
              dbref="PRM.SelKondisi"
              height="95px"
            />
            <!-- <div class="info-red">
              *) Data harus diisi untuk bisa masuk ERTLH
            </div> -->
          </div>
        </div>
        <div v-show="tabId == 'Kesehatan' || isMobile">
          <div style="padding: 10px" :class="isMobile ? '' : 'form-inline'">
            <XSelect
              v-model:value="forms.AdaJendela"
              label="Jendela"
              :items="[
                { val: '1', txt: 'Ada Jendela' },
                { val: '0', txt: 'Tidak Ada' },
              ]"
            />
            <XSelect
              v-model:value="forms.AdaVentilasi"
              label="Ventilasi"
              :items="[
                { val: '1', txt: 'Ada Ventilasi' },
                { val: '0', txt: 'Tidak Ada' },
              ]"
            />
            <XSelect
              v-model:value="forms.SumberAir"
              label="Sumber Air*"
              dbref="PRM.SelSumberAir"
            />
            <XSelect
              v-model:value="forms.KamarMandi"
              label="Kmr Mandi/Jamban*"
              dbref="PRM.SelKamarMandi"
            />
            <XSelect
              v-model:value="forms.JenisKloset"
              label="Jenis Jamban*"
              dbref="PRM.SelKloset"
            />
            <XSelect
              v-model:value="forms.JenisSeptikTank"
              label="Jenis TPA*"
              dbref="PRM.SelSepticTank"
            />
            <XSelect
              v-model:value="forms.JarakSepticTank"
              label="Jarak Septik Tank*"
              :items="[
                { val: 0, txt: '&lt; 10m' },
                { val: 1, txt: '&gt;= 10m' },
              ]"
            />
            <XSelect
              v-model:value="forms.Penerangan"
              label="Sumber Penerangan*"
              dbref="PRM.SelPenerangan"
            />
            <XSelect
              v-model:value="forms.BBMasak"
              label="BB Masak*"
              :items="[
                { val: 1, txt: 'Listrik / Gas' },
                { val: 2, txt: 'Minyak Tanah' },
                { val: 3, txt: 'Arang / Kayu' },
                { val: 4, txt: 'Lainnya' },
              ]"
            />
            <!-- <div class="info-red">
              *) Data harus diisi untuk bisa masuk ERTLH
            </div> -->
          </div>
        </div>
        <div v-show="tabId == 'Foto' || isMobile">
          <div
            style="padding-left: 10px; width: 100%"
            :class="isMobile ? '' : 'form-inline'"
          >
            <div>
              <Map
                v-show="!isMobile"
                :ref="'map'"
                v-model:lat="forms.GeoLat"
                v-model:lon="forms.GeoLng"
                width="100%"
                height="378px"
                :searchbox="true"
                @change="onMapChange"
              />
            </div>
            <XLabel v-show="isMobile" label="Titik Lokasi">
              <XInput style="widthv-model: 50%" :value="forms.GeoLat" />
              <XInput style="widthv-model: 50%" :value="forms.GeoLng" />
            </XLabel>
            <v-btn
              v-show="isMobile"
              color="primary"
              style="width: 100%; margin-bottom: 12px"
              @click="getLocation"
            >
              <v-icon left>mdi-crosshairs-gps</v-icon>
              TITIK SAAT INI
            </v-btn>
            <!-- <v-btn @click="applyGpsFromPhoto" style="margin-top: -80px">
              Terapkan GPS berdasarkan foto
            </v-btn> -->
            <div>
              <Uploader
                v-model:value="forms.Profile"
                label="FOTO DIRI*"
                accept=".jpg,.jpeg,.jfif,.png,.heic,.jfif,.png,.heic"
                @change="updateMapLocation"
              ></Uploader>
              <Uploader
                v-model:value="forms.RumahDepan"
                label="FOTO DEPAN*"
                accept=".jpg,.jpeg,.jfif,.png,.heic,.jfif,.png,.heic"
                @change="updateMapLocation"
              >
              </Uploader>
              <Uploader
                v-model:value="forms.RumahSamping"
                label="FOTO SAMPING*"
                accept=".jpg,.jpeg,.jfif,.png,.heic,.jfif,.png,.heic"
                @change="updateMapLocation"
              >
              </Uploader>
              <Uploader
                v-model:value="forms.Rumah0"
                :label="'FOTO LAHAN' + (forms.TanahLain == '1' ? '*' : '')"
                accept=".jpg,.jpeg,.jfif,.png,.heic,.jfif,.png,.heic"
              >
              </Uploader>
            </div>
          </div>
        </div>
        <div v-show="tabId == 'Lentera' || isMobile">
          <div style="padding: 10px" :class="isMobile ? '' : 'form-inline'">
            <div style="margin-bottom: 8px; font-weight: bold">
              PEMINATAN PROGRAM
            </div>
            <XSelect
              v-model:value="forms.AdaMinat"
              label="Berminat Pembiayaan"
              :items="[
                { val: '1', txt: 'Berminat' },
                { val: '2', txt: 'Tidak Berminat' },
              ]"
            />
            <XSelect
              v-model:value="forms.JenisMinat"
              label="Jenis Peminatan"
              :disabled="disableProgram"
              :items="[
                { val: '1', txt: 'Milik' },
                { val: '2', txt: 'Sewa (Rusunawa)' },
              ]"
            />
            <XSelect
              v-model:value="forms.ProgramPembiayaan"
              label="Program Pembiayaan"
              :disabled="disableProgram"
              :items="opsiPembiayaan"
            />
            <XLabel
              v-show="forms.ProgramPembiayaan == 3"
              label="Jenis Perbaikan"
            >
              <div>
                <Checkbox
                  v-model:value="forms.PerbaikanRumah"
                  text="Perbaikan Rumah / Existing"
                />
                <Checkbox
                  v-model:value="forms.PerbaikanPenambahan"
                  text="Penambahan Ruang / Lainnya"
                />
                <Checkbox
                  v-model:value="forms.PerbaikanLainnya"
                  text="Perbaikan Lainnya"
                />
              </div>
            </XLabel>
            <XLabel
              v-show="['2', '3'].includes(forms.ProgramPembiayaan)"
              label="Ada Swadaya?"
            >
              <div>
                <Checkbox
                  v-model:value="forms.SwadayaUang"
                  text="Tabungan Uang"
                />
                <Checkbox
                  v-model:value="forms.SwadayaMeterial"
                  text="Tabungan Material"
                />
                <Checkbox
                  v-model:value="forms.SwadayaTenaga"
                  text="Tenaga Kerja"
                />
              </div>
            </XLabel>
            <XSelect
              v-model:value="forms.BankPelaksana"
              label="Bank Pelaksana"
              :disabled="disableProgram"
              :items="[
                { val: '1', txt: 'Bank BTN' },
                { val: '2', txt: 'Bank BRI' },
                { val: '4', txt: 'Bank Mandiri' },
                { val: '5', txt: 'Bank BNI' },
                { val: '6', txt: 'Bank Jateng' },
                { val: '7', txt: 'Bank BCA' },
                { val: '3', txt: 'Bank Lainnya' },
              ]"
            />
            <XSelect
              v-model:value="forms.BesarCicilan"
              label="Besar Cicilan"
              :disabled="disableProgram"
              :items="[
                { val: '1', txt: '< 500 ribu' },
                { val: '2', txt: '500 ribu - 750 ribu' },
                { val: '3', txt: '750 ribu - 1 juta' },
                { val: '4', txt: '> 1 juta' },
              ]"
            />
            <XSelect
              v-model:value="forms.TahunRencana"
              label="Tahun Rencana"
              :disabled="disableProgram"
              :items="[
                { val: '2025', txt: '2025' },
                { val: '2026', txt: '2026' },
                { val: '2027', txt: '2027' },
                { val: '0', txt: 'Belum Tahu' },
              ]"
            />
            <div style="margin: 10px 0 8px 0; font-weight: bold">
              KAWASAN IKLIM
            </div>
            <XLabel label="Sarana Transportasi">
              <div>
                <Checkbox
                  v-model:value="forms.AngkutanUmum"
                  text="Angkutan Umum"
                />
                <Checkbox v-model:value="forms.Terminal" text="Terminal" />
                <Checkbox v-model:value="forms.Stasiun" text="Stasiun" />
                <Checkbox v-model:value="forms.Pasar" text="Pasar" />
                <Checkbox v-model:value="forms.Bank" text="Bank" />
                <Checkbox v-model:value="forms.GerbangTol" text="Gerbang Tol" />
                <Checkbox v-model:value="forms.SPBU" text="SPBU" />
              </div>
            </XLabel>
            <XLabel label="Sarana Pendidikan">
              <div>
                <Checkbox v-model:value="forms.TK" text="TK" />
                <Checkbox v-model:value="forms.SD" text="SD" />
                <Checkbox v-model:value="forms.SLTP" text="SLTP" />
                <Checkbox v-model:value="forms.SLTA" text="SLTA" />
                <Checkbox
                  v-model:value="forms.Universitas"
                  text="Universitas"
                />
                <Checkbox v-model:value="forms.SekolahLainnya" text="Lainnya" />
              </div>
            </XLabel>
            <XLabel label="Tempat Ibadah">
              <div>
                <Checkbox v-model:value="forms.Masjid" text="Masjid" />
                <Checkbox v-model:value="forms.Gereja" text="Gereja" />
                <Checkbox v-model:value="forms.Vihara" text="Vihara" />
                <Checkbox v-model:value="forms.Klenteng" text="Klenteng" />
                <Checkbox v-model:value="forms.IbadahLainnya" text="Lainnya" />
              </div>
            </XLabel>
            <XSelect
              v-model:value="forms.GenanganAir"
              label="Genangan Air"
              :items="[
                { val: '1', txt: 'Pernah' },
                { val: '2', txt: 'Tidak Pernah' },
              ]"
            />
            <XSelect
              v-model:value="forms.Banjir"
              label="Banjir"
              :items="[
                { val: '1', txt: 'Pernah' },
                { val: '2', txt: 'Tidak Pernah' },
              ]"
            />
            <XSelect
              v-model:value="forms.PutingBeliung"
              label="Puting Beliung"
              :items="[
                { val: '1', txt: 'Pernah' },
                { val: '2', txt: 'Tidak Pernah' },
              ]"
            />
            <XSelect
              v-model:value="forms.KeretakanTanah"
              label="Keretakan Tanah"
              :items="[
                { val: '1', txt: 'Pernah' },
                { val: '2', txt: 'Tidak Pernah' },
              ]"
            />
            <XSelect
              v-model:value="forms.Longsor"
              label="Longsor"
              :items="[
                { val: '1', txt: 'Pernah' },
                { val: '2', txt: 'Tidak Pernah' },
              ]"
            />
            <XSelect
              v-model:value="forms.GempaBumi"
              label="Gempa Bumi"
              :items="[
                { val: '1', txt: 'Pernah' },
                { val: '2', txt: 'Tidak Pernah' },
              ]"
            />
          </div>
        </div>
        <!-- <div v-show="tabId == 'FAQ' || isMobile">
          <Panel
            dbref="PRM.SelWhyPBDTEdit"
            :dbparams="{ NoRef: forms.NoRef }"
            :autobind="false"
            style="padding:10px; font-size:14px;"
          >
            <template #default="{ first }">
              <div>Alasan tidak masuk input usulan:</div>
              <div>{{ first.Message }}</div>
            </template>
          </Panel>
        </div> -->
      </div>
    </div>
  </Modal>
</template>
<script>
import moment from 'moment'
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    noRef: [String, Number],
    nik: [String, Number],
    area: Object,
    disabled: Boolean,
    igahp: Boolean,
    igahpData: Object,
  },
  data: () => ({
    x_show: false,
    loading: false,
    saving: false,
    error: '',
    errorAction: '',
    warning: '',
    isBDT: false,

    forms: {},
    tabId: 'Individu',
    disableProgram: true,
    photoGpsTag: null,
  }),
  computed: {
    limaTahun() {
      let tahun = []
      for (let i = 0; i < 5; i++) {
        tahun.push({ val: moment().year() - i, txt: moment().year() - i })
      }
      return tahun
    },
    opsiPembiayaan() {
      if (this.forms.JenisMinat == '1') {
        return [
          { val: '1', txt: 'Pemilikan Rumah' },
          { val: '2', txt: 'Pembangunan Rumah' },
          { val: '3', txt: 'Perbaikan Rumah' },
          { val: '5', txt: 'Pemilikan Rusun' },
        ]
      } else if (this.forms.JenisMinat == '2') {
        return [{ val: '4', txt: 'Sewa Hunian' }]
      } else {
        return []
      }
    },
  },
  watch: {
    show(val) {
      this.x_show = val
      this.tabId = 'Individu'
      this.error = ''
      this.errorAction = ''
    },
    x_show(val) {
      if (!val) {
        this.forms.RumahDepan = ''
        this.forms.RumahSamping = ''
        this.forms = {}
        this.photoGpsTag = null
      } else this.populate()
      this.$emit('update:show', val)
    },
    'forms.NIK'(val) {
      if (val && val.length == 16) {
        this.CheckDinsos()
      } else {
        this.forms.NewTipeData = null
      }
    },
    'forms.AdaMinat'(val) {
      if (val == '1') {
        this.disableProgram = false
      } else {
        this.disableProgram = true
        this.forms.JenisMinat = null
        this.forms.ProgramPembiayaan = null
        this.forms.BankPelaksana = null
        this.forms.BesarCicilan = null
        this.forms.TahunRencana = null
      }
    },
  },
  methods: {
    async populate() {
      this.saving = false
      const lastData = localStorage.getItem('igahp-' + this.nik)
      if (lastData) {
        if (confirm('Ada data yg belum tersimpan, tampilkan?')) {
          this.forms = JSON.parse(lastData)
          return
        } else {
          localStorage.removeItem('igahp-' + this.nik)
        }
      }
      this.loading = true
      this.isBDT = false
      if (this.noRef || this.nik) {
        let { data } = await this.$api.call('PRM_SelPBDTDetail', {
          NoRef: this.noRef,
          NIK: this.nik,
        })
        this.forms = data?.[0] || {
          ...this.igahpData,
          Sumber: null,
          Tahun: null,
        }
      } else {
        this.forms = {
          NIK: '',
          Nama: '',
          Alamat: '',
          IDBDT: '',
          NewTipeData: '',
          Sumber: null,
          Tahun: null,
        }
      }
      this.loading = false
    },
    async CheckDinsos() {
      if (
        this.forms.NIK &&
        (this.forms.NIK + '').length == 16 &&
        !(200 > this.forms.TipeData || this.isBDT)
      ) {
        let { data } = await this.$api.call('PRM_SelPBDTDetail', {
          NIK: this.forms.NIK,
        })
        if (data.length > 0) {
          this.forms = data?.[0]
          this.setTanggalLahir()
        }

        let d = await this.$api.post(
          this.$api.url + '/api/dinsos/get/' + this.forms.NIK
        )
        if (d.success) {
          if (this.forms.TipeData > 200 && this.forms.TipeData < 300) {
            this.forms.NewTipeData -= new Date().getYear()
          }
          this.isBDT = true

          let alamat =
            (d.data.ALAMAT || d.data.alamat || '').trim() +
            ' RT ' +
            (d.data.RT || d.data.rt || '?') +
            ' RW ' +
            (d.data.RW || d.data.rw || '?')
          this.forms.Nama = (
            this.forms.Nama ||
            d.data.Nama ||
            d.data.nama
          ).trim()
          if (
            alamat.length > this.forms.Alamat ||
            this.forms.Alamat.match(/^ - /)
          ) {
            this.forms.Alamat = alamat
          }

          this.forms.IDBDT = d.data.IDBDT || d.data.id_dtks || d.data.idjtg
          this.forms.NIKBDT = d.data.nik || this.forms.NIK
          this.forms.NamaBDT = (d.data.Nama || d.data.nama).trim()
          this.forms.NewTipeData = new Date().getYear()
        } else {
          // this.$api.notify('Belum terdaftar di BDT', 'error')
        }
        // this.setTanggalLahir()
      }
    },
    async SaveIGAHP(d) {
      let igahpUser = {}
      if (sessionStorage.getItem('igahpUser')) {
        igahpUser = JSON.parse(sessionStorage.getItem('igahpUser'))
      }
      await this.$api.call('OUT.SavLenteraHijau', d)
      let r2 = await this.$api.post(this.$api.url + '/api/igahp/sync', {
        ...igahpUser,
        NIK: d.NIK,
      })
      if (!d.NoRef) {
        this.$api.post(this.$api.url + '/api/igahp/check', {
          nik: d.NIK,
          nama: d.Nama,
        })
      }
      return r2
    },
    async Save() {
      const frms = { ...this.forms }
      localStorage.setItem('igahp-' + frms.NIK, JSON.stringify(frms))
      // const { Profile, RumahDepan, RumahSamping, Rumah0 } = this.forms
      // if (
      //   (Profile && [RumahDepan, RumahSamping, Rumah0].includes(Profile)) ||
      //   (RumahDepan && [Profile, RumahSamping, Rumah0].includes(RumahDepan)) ||
      //   (RumahSamping &&
      //     ![Profile, RumahDepan, Rumah0].includes(RumahSamping)) ||
      //   (Rumah0 && [Profile, RumahDepan, RumahSamping].includes(Rumah0))
      // ) {
      //   this.$api.notify('Foto tidak boleh sama', 'error')
      //   return
      // }
      if (!frms.NIK || !frms.Nama) {
        this.$api.notify('NIK/Nama tidak boleh kosong', 'error')
        return
      }
      if (frms.Alamat?.match(/RT \?/)) {
        this.$api.notify('Masukkan Alamat dengan benar', 'error')
        return
      }

      this.saving = true
      this.error = ''
      let ret = await this.$api.call(
        'PRM.SavPBDTDetail',
        {
          ...this.forms,
          Kabupaten: this.area.Kabupaten,
          Kecamatan: this.area.Kecamatan,
          Kelurahan: this.area.Kelurahan,
          KodeWilayah: this.area.KodeDagri,
        },
        { silent: true }
      )
      if (ret.message.match(/Double data/)) {
        ret.success = true
      }
      let r2 = await this.SaveIGAHP(frms, true) //!ret.success || !this.forms.NoRef
      if (ret.success && r2.success) {
        // this.$api.post(this.$api.url + '/api/ertlh/update/' + this.forms.NIK)
        this.$api.notify('Data berhasil disimpan')
        this.x_show = false
        this.$emit('save')
      } else if (!ret.success) {
        this.$api.notify(ret.message, 'error')
        this.error = ret.message
        this.errorAction = ret.data[0].ErrAction
      } else if (!r2.success) {
        this.$api.notify(r2.message, 'error')
        this.error = r2.message
      }
      if (this.error) this.$emit('error', this.error)
      else {
        localStorage.removeItem('igahp-' + frms.NIK)
      }
      this.saving = false
    },
    setTanggalLahir() {
      const nik = this.forms.NIK || this.nik
      if (!this.forms.TanggalLahir) {
        let strdate =
          '19' +
          nik.substr(10, 2) +
          '-' +
          nik.substr(8, 2) +
          '-' +
          nik.substr(6, 2)

        if (moment(strdate).isValid()) this.forms.TanggalLahir = strdate
      }
    },
    async errorActionYes(act) {
      this.error = ''
      this.errorAction = ''
      let ret = {}

      if (act.match(/pindah desa/)) {
        ret = await this.$api.call('PRM.SavPindahDesa', {
          ...this.forms,
          Kabupaten: this.area.Kabupaten,
          Kecamatan: this.area.Kecamatan,
          Kelurahan: this.area.Kelurahan,
          KodeWilayah: this.area.KodeDagri,
        })
      } else if (act.match(/data anda benar/)) {
        ret = await this.$api.call('PRM.SavDoubleNIK', {
          ...this.forms,
        })
      }
      if (!ret.success) {
        this.error = ret.message
        this.errorAction = ret.data[0].ErrAction
      }
    },
    async errorActionNo() {
      this.error = ''
      this.errorAction = ''
    },
    async Delete() {
      if (!confirm(`Anda yakin akan menghapus ${this.forms.Nama}?`)) return

      let ret = await this.$api.call('PRM.DelPBDTedit', {
        ...this.forms,
        Kabupaten: this.area.Kabupaten,
        Kecamatan: this.area.Kecamatan,
        Kelurahan: this.area.Kelurahan,
        KodeWilayah: this.area.KodeDagri,
      })
      if (ret.success) {
        this.x_show = false
        this.$emit('delete')
      } else this.error = ret.message
    },
    updateMapLocation(res) {
      if (res.meta && res.meta.gps && res.meta.gps.lat) {
        this.photoGpsTag = {
          lat: res.meta.gps.lat,
          lon: res.meta.gps.lon,
        }
        this.applyGpsFromPhoto()
      }
    },
    applyGpsFromPhoto() {
      if (this.photoGpsTag) {
        this.forms.GeoLat = this.photoGpsTag.lat
        this.forms.GeoLng = this.photoGpsTag.lon
        this.photoGpsTag = null
      }
    },
    getLocation() {
      if (window.navigator.geolocation) {
        window.navigator.geolocation.getCurrentPosition(
          (pos) => {
            this.forms.GeoLat = pos.coords.latitude
            this.forms.GeoLng = pos.coords.longitude
          },
          (err) => {
            switch (err.code) {
              case err.PERMISSION_DENIED:
                alert('User denied the request for Geolocation.')
                break
              case err.POSITION_UNAVAILABLE:
                alert('Location information is unavailable.')
                break
              case err.TIMEOUT:
                alert('The request to get user location timed out.')
                break
              case err.UNKNOWN_ERROR:
                alert('An unknown error occurred.')
                break
            }
          }
        )
      } else {
        alert('Geolocation is not supported by this browser.')
      }
    },
    async onMapChange() {
      this.forms.RDTR = null
      this.$refs.map.setTooltip('')
      let params = {
        lat: this.forms.GeoLat,
        lon: this.forms.GeoLng,
        kab: this.forms.Kabupaten,
        kec: this.forms.Kecamatan,
      }
      let d = await this.$api.get(
        this.$api.url + '/api/gistaru?' + new URLSearchParams(params).toString()
      )
      let attr = d.data?.features?.[0]?.attributes
      if (attr) {
        this.forms.RDTR = attr.NAMOBJ
        this.$refs.map.setTooltip(
          `<div>${attr.NAMZON || ''}</div><div>${attr.NAMOBJ}</div>`,
          {
            style: {
              color: ['RTH', 'BJ'].includes(attr.KODZON) ? 'white' : 'black',
              backgroundColor: ['RTH', 'BJ'].includes(attr.KODZON)
                ? 'rgba(244, 57, 64, 0.8)'
                : 'rgba(255,255,255,0.7)',
            },
          }
        )
      }
    },
  },
}
</script>
<style lang="scss">
.modal-validasi-detail {
  .ui-dropdown--button {
    width: 250px !important;
  }
  .ui-input {
    .--input {
      width: 250px !important;
    }
  }
  .form-label {
    flex: 0 0 180px;
  }
}
.vert-tab {
  width: 50px;
  height: 50px;
  vertical-align: top;
  cursor: pointer;
  text-align: center;
  border-bottom: 1px solid silver;

  .v-icon {
    margin-top: 8px;
    color: silver;
  }
}

.vert-tab.active {
  background: #f3f3f3;
  position: relative;
  z-index: 2;
  .v-icon {
    color: #333;
  }
}

.info-red {
  padding-top: 8px;
  font-size: 12px;
  color: red;
}

.imgbox {
  width: 150px;
  height: 150px;
  border-right: 1px solid white;
  box-sizing: border-box;
}
</style>
