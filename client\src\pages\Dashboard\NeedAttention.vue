<template>
  <div v-show="tasks.length">
    <div
      style="padding:10px; background:white; font-weight:bold; font-size:small"
    >
      BUTUH DIKERJAKAN
    </div>
    <v-expansion-panels dense>
      <v-expansion-panel v-for="(item, i) in tasks" :key="i" dense>
        <v-expansion-panel-header>
          <div style="font-size:small">
            {{ item }}
            <v-badge
              color="error"
              inline
              :content="childs[item][0].JmlTotal"
            ></v-badge>
          </div>
        </v-expansion-panel-header>
        <v-expansion-panel-content>
          <div v-for="(ch, j) in childs[item]" :key="j">
            <v-btn variant="text" color="primary" size="small" @click="Redirect(item, ch)">
              DS. {{ ch.<PERSON><PERSON> }}, {{ ch.<PERSON> }}
            </v-btn>
            <v-badge color="error" inline :content="ch.Jml"></v-badge>
          </div>
        </v-expansion-panel-content>
      </v-expansion-panel>
    </v-expansion-panels>
  </div>
</template>
<script>
export default {
  data: () => ({
    tasks: [],
    childs: {},
  }),
  mounted() {
    this.Populate()
  },
  methods: {
    async Populate() {
      let d = await this.$api.call('PRM_SelNeedAttention', { nocache: true })

      for (let t of d.data) {
        if (this.tasks[this.tasks.length - 1] != t.TaskName)
          this.tasks.push(t.TaskName)
        if (!this.childs[t.TaskName]) this.childs[t.TaskName] = []
        if (this.childs[t.TaskName].length < 5) this.childs[t.TaskName].push(t)
        this.childs[t.TaskName][0].JmlTotal =
          (this.childs[t.TaskName][0].JmlTotal || 0) + t.Jml
      }
      // this.tasks = d.data
    },
    Redirect(page, params) {
      if (page == 'FINALISASI PROPOSAL')
        this.$router.push({
          path: '/Main/RTLH/FinalisasiUsulan',
          query: params,
        })
      else if (page == 'BERKAS PENYALURAN')
        this.$router.push({
          path: '/Main/RTLH/InputBerkas',
          query: params,
        })
      else if (page == 'MONEV')
        this.$router.push({
          path: '/Main/RTLH/Monev/',
          query: params,
        })
      else if (page == 'LPJ')
        this.$router.push({
          path: '/Main/RTLH/LPJ/',
          query: params,
        })
    },
  },
}
</script>
