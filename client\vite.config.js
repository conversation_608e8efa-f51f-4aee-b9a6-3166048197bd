import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
// import Components from 'unplugin-vue-components/vite'
// import { VuetifyResolver } from 'unplugin-vue-components/resolvers'
import vuetify from 'vite-plugin-vuetify'
import { VitePWA } from 'vite-plugin-pwa'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vuetify({
      autoImport: true, // Auto-import components and directives
    }),
    // Components({
    //   resolvers: [VuetifyResolver()],
    // }),
    VitePWA({
      registerType: 'autoUpdate',
      injectRegister: 'auto',
      // devOptions: {
      //   enabled: true,
      // },
      manifest: {
        name: 'Simperum',
        short_name: 'Simperum',
        description: 'Sistem Informasi Perumahan Disperakim Jawa Tengah',
        theme_color: '#262626',
        background_color: '#ffffff',
        icons: [
          {
            src: 'imgs/pwa144.png',
            sizes: '144x144',
            type: 'image/png',
            purpose: 'maskable',
          },
          {
            src: 'imgs/pwa512.png',
            sizes: '512x512',
            type: 'image/png',
            purpose: 'maskable',
          },
        ],
      },
      workbox: {
        clientsClaim: true,
        skipWaiting: true,
        navigateFallbackDenylist: [/\/uploads/, /\/reports?/],
      },
    }),
  ],
  optimizeDeps: {
    exclude: ['vue'],
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
})
