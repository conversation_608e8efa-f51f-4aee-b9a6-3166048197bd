<template>
  <div>
    <div style="display: flex">
      <XSelect
        v-model:value="proposal"
        dbref="PRM.SelProposal"
        :value-as-object="true"
        width="95px"
        style="margin-right: 10px"
      />
      <XSelect
        v-model:value="forms.Kabupaten"
        style="margin-right: 10px"
        value-key="txt"
        dbref="PRM.SelPBDTCity"
        :dbparams="{ nocache: true }"
      />
      <XSelect
        v-model:value="forms.Sumber"
        :disabled="!forms.Kabupaten"
        dbref="PRM.SelAlokasiSumber"
        width="100px"
      />
      <Checkbox
        v-model:value="hasAllocation"
        :disabled="!forms.Kabupaten"
        style="margin-left: 10px; margin-top: 2px"
        text="Mempunyai Alokasi"
      />
    </div>
    <Grid
      v-model:datagrid="forms.XmlAlokasi"
      dbref="PRM.Alokasi"
      :dbparams="params"
      :filter="filterGrid"
      :disabled="true"
      :autopaging="false"
      height="calc(100vh - 240px)"
      :columns="[
        {
          name: 'Kecamatan',
          value: 'Kecamatan',
          width: '150px',
        },
        {
          name: 'Kelurahan',
          value: 'Kelurahan',
          width: '150px',
        },
        {
          name: 'Kuota',
          value: 'Kuota',
          class: 'plain',
        },
      ]"
    >
      <template #row-Kuota="{ row }">
        <XInput v-model:value="row.Kuota" type="number" width="95px" />
      </template>
    </Grid>
    <br />
    <v-btn
      v-show="forms.Kabupaten && forms.Sumber"
      color="primary"
      @click="Save"
      >SIMPAN</v-btn
    >
  </div>
</template>
<script>
export default {
  data: () => ({
    proposal: {
      InputName: new Date().getFullYear(),
    },
    forms: { XmlAlokasi: [] },
    hasAllocation: false,
  }),
  computed: {
    params() {
      let { Kabupaten, Sumber } = this.forms
      let { InputName } = this.proposal
      return { Kabupaten, Sumber, Tahun: InputName }
    },
  },
  methods: {
    async Save() {
      await this.$api.call('PRM_SavAlokasi', {
        ...this.forms,
        Tahun: this.proposal.InputName,
      })
    },
    filterGrid(row) {
      return this.hasAllocation ? Boolean(row.Kuota) : true
    },
  },
}
</script>
<style lang="scss"></style>
