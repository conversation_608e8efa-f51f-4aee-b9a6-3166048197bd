<template>
  <Modal
    title="CHECKLIST TERMIN 1"
    v-model:show="xshow"
    width="450px"
    @onSubmit="Save"
  >
    <div style="display: flex" class="form-inline">
      <!-- <div class="iblock">
        <Uploader
          label="KTP"
          v-if="!isMobile"
          v-model:value="forms.KTP"
          accept=".pdf"
        >
        </Uploader>
      </div> -->
      <div class="iblock" style="width: 400px">
        <div style="padding: 5px">
          <CheckUpload
            v-model:value="forms.CekRekeningVA"
            label="Rekening VA"
            accept=".pdf"
          ></CheckUpload>
          <CheckUpload
            v-model:value="forms.CekPaktaIntegritas"
            label="Pakta Integritas"
            accept=".pdf"
          ></CheckUpload>
          <CheckUpload
            v-model:value="forms.CekSKPenerima"
            label="SK Penerima Bantuan"
            accept=".pdf"
          ></CheckUpload>
          <CheckUpload
            v-model:value="forms.CekPengukuhanPokmas"
            label="Surat Pengukuhan Pokmas"
            accept=".pdf"
          ></CheckUpload>
          <CheckUpload
            v-model:value="forms.CekSwakelola"
            label="Kontrak Swakelola"
            accept=".pdf"
          ></CheckUpload>
          <div style="display: flex">
            <CheckUpload
              v-model:value="forms.CekRPD"
              label="RPD"
              accept=".pdf"
            ></CheckUpload>
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="DownloadRPD"
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              v-model:value="forms.CekCaraPengadaan"
              label="Surat Pernyataan Cara Pengadaan"
              accept=".pdf"
            ></CheckUpload>
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="Download('2_SURAT_PERNYATAAN_CARA_PENGADAAN.docx')"
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              v-model:value="forms.CekPermohonanPembayaran"
              label="Permohonan Pembayaran"
              accept=".pdf"
            ></CheckUpload>
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="Download('3_SURAT_PERMOHONAN_PEMBAYARAN_T1.docx')"
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              v-model:value="forms.CekBAPembayaran"
              label="BA Pembayaran"
              accept=".pdf"
            ></CheckUpload>
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="
                Download(
                  '4_BERITA_ACARA_PEMBAYARAN_UNTUK_UANG_MUKA_KPA_KE_POKMAS.docx'
                )
              "
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              v-model:value="forms.CekSPTJB"
              label="Tanggung Jawab Uang Muka"
              accept=".pdf"
            ></CheckUpload>
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="Download('5_SUPER_TANGGUNG_JAWAB_UANG_MUKA.docx')"
            >
              mdi-download
            </v-icon>
          </div>
          <!-- <CheckUpload
            v-model:value="forms.CekSPTJB"
            label="SPTJB"
            accept=".pdf"
          ></CheckUpload> -->
        </div>
      </div>
    </div>
    <!-- <template v-slot:left-action>
      <div style="padding-left: 10px; color: gray;">
        <Checkbox text="Check Semua" v-model:value="checkAll" />
      </div>
    </template> -->
  </Modal>
</template>
<script>
import CheckUpload from './CheckUpload.vue'
export default {
  components: {
    CheckUpload,
  },
  data: () => ({
    xshow: false,
    forms: {},
    checkAll: false,
  }),
  props: {
    show: Boolean,
    kodedagri: [String, Number],
    tahun: [String, Number],
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      this.$emit('update:show', val)
    },
    kodedagri(val) {
      if (val) this.populate()
    },
    checkAll(val) {
      for (let x in this.forms) {
        if (x.match(/^Cek/)) this.forms[x] = val
      }
    },
  },
  methods: {
    async populate() {
      this.forms = {}
      let { data } = await this.$api.call('RLK.SelPencairanCek', {
        KodeDagri: this.kodedagri,
        Tahun: this.tahun,
      })

      this.forms = data.length ? data[0] : {}
    },
    DownloadRPD() {
      window.open(
        this.$api.url +
          '/report/backlog/doc/RPD.ods?out=xlsx&Tahun=' +
          this.tahun +
          '&KodeDagri=' +
          this.kodedagri
      )
    },
    Download(params) {
      // console.log(
      //   this.$api.url +
      //     '/report/backlog/doc/' +
      //     params +
      //     `?Tahun=${this.tahun}&KodeDagri=${this.kodedagri}&sp=RLK_RptDocPencairan`
      // )
      window.open(
        this.$api.url +
          '/report/backlog/doc/' +
          params +
          `?Tahun=${this.tahun}&KodeDagri=${this.kodedagri}&sp=RLK_RptDocPencairan`
      )
    },
    async Save() {
      let ret = await this.$api.call('RLK.SavPencairanCek', {
        ...this.forms,
        KodeDagri: this.kodedagri,
        Tahun: this.tahun,
      })
      if (ret.success) this.$emit('update:show', false)
    },
  },
}
</script>
<style lang="scss">
.modal-proposal {
  .v-dialog--content {
    padding: 0;
  }
}
</style>
