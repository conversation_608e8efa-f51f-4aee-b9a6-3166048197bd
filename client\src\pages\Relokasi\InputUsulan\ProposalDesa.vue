<template>
  <Modal
    title="PROPOSAL DESA"
    v-model:show="xshow"
    width="900px"
    @onSubmit="Save"
    :disabled="disabled"
  >
    <div style="padding: 25px">
      <Uploader
        label="Proposal"
        v-model:value="forms.ProposalDesa"
        accept=".pdf"
      ></Uploader>
    </div>
  </Modal>
</template>
<script>
import { mapGetters } from 'vuex'
export default {
  components: {},
  data: () => ({
    xshow: false,
    forms: { ProposalDesa: null },
    showRAB: false,
    rab: {},
  }),
  props: {
    show: Boolean,
    kodeDagri: [String, Number],
    tahun: [String, Number],
    disabled: Boolean,
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      if (!val) this.forms = { ProposalDesa: null }
      else if (this.xshow) this.populate()
      this.$emit('update:show', val)
    },
  },
  computed: {
    ...mapGetters({
      user: 'getUser',
    }),
    rabFilters() {
      let f = ['A', 'B', 'C', 'D', 'E']
      // console.log(f)
      return f
    },
  },
  methods: {
    async populate() {
      this.loading = true
      if (this.kodeDagri) {
        let { data } = await this.$api.call('RLK.SelBerkas', {
          KodeDagri: this.kodeDagri,
          Tahun: this.tahun,
          GroupId: 'proposal',
          Tipe: 'ProposalDesa',
        })

        if (data.length) this.forms.ProposalDesa = data[0].Url
      } else {
        this.forms = { ProposalDesa: null }
      }
      this.loading = false
    },
    async Save() {
      this.error = ''
      let ret = await this.$api.call('RLK.SavBerkas', {
        JsonBerkas: [
          {
            KodeDagri: this.kodeDagri,
            Tahun: this.tahun,
            GroupId: 'proposal',
            Tipe: 'ProposalDesa',
            Url: this.forms.ProposalDesa,
          },
        ],
      })
      if (ret.success) {
        this.xshow = false
        this.$emit('save')
      } else this.error = ret.message
    },
  },
}
</script>
<style lang="scss">
.modal-proposal {
  .v-dialog--content {
    padding: 0;
  }
}
</style>
