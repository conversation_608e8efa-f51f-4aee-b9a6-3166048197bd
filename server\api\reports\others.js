var express = require('express')
var router = express.Router()
var Reporter = require('./generator')
const path = require('path')
const fs = require('fs')
// const PDFMerger = require('pdf-merger-js')
const carbone = require('carbone')
var db = require('../../common/db')
var moment = require('moment')
const archiver = require('archiver')
const pdfmerge = require('easy-pdf-merge');
const {resolve} = require('path')
const shell = require('shelljs')
moment.locale('id')

router.get('/', async function(req, res) {
    
  res.send({
    success: true,
    data: `/get/kwitansi.pdf`,
    message: 'Report Generated',
    type: 'url',
  })
  
})

const mergePdf = (source_files, dest_file_path) => {
  return new Promise((resolve, reject) => {
    pdfmerge(source_files, dest_file_path, function (err) {
      if (err) {
        console.error(err)
      }
      resolve()
    });
  })
}

const generatePdfBerkas = async (body) => {
  let data = await db
    .exec('PRM_RptBerkasPenyaluran', body, true)
    .catch(err => console.error(err))

  let {_PRM_SelKabupatenID, _PRM_SelProposalID, _PRM_SelRekomID} = body;
  let addf = moment().format('mmss')
  let postfix = `${_PRM_SelKabupatenID}-${_PRM_SelProposalID+2013}-${_PRM_SelRekomID.replace('REKOM ','')}-${addf}`

  let kwitansi = []; //new PDFMerger();
  let ktpKades = []; //new PDFMerger();
  let ktpBendahara = []; //new PDFMerger();
  let bukuRekening = []; //new PDFMerger();
  let csv = `Kabupaten,Kecamatan,Desa,KTP Kades, Kwitansi, Buku Rekening\r\n`
  for(const d of data){
    let line = [d.Kabupaten, d.Kecamatan, d.Kelurahan]
    if(d.KtpKades && d.KtpKades.match(/\.pdf$/i)) {
      let stat = fs.statSync(d.KtpKades.replace(/^\//,''))
      if(!stat.size) console.log(d.KtpKades)
      ktpKades.push(d.KtpKades.replace(/^\//,''))
      line.push(d.KtpKades)
    } else if(d.KtpKades) {
      let ff = d.KtpKades.split('/')
      shell.exec(
        `/opt/libreoffice7.0/program/soffice --convert-to pdf ${d.KtpKades} --outdir /app/tmp`,
        {
          silent: true,
        }
      )
      line.push('/app/tmp/'+ff[ff.length-1].replace(/(jpg|docx)/,'pdf'))
    } else {
      line.push('')
    }
    // if(d.KtpBendahara && d.KtpBendahara.match(/\.pdf$/)) {
    //   ktpBendahara.push(d.KtpBendahara.replace(/^\//,''))
    //   line.push(d.KtpBendahara)
    // } else {
    //   line.push('')
    // }
    if(d.Kwitansi && d.Kwitansi.match(/\.pdf$/i)) {
      kwitansi.push(d.Kwitansi.replace(/^\//,''))
      line.push(d.Kwitansi)
    } else if(d.Kwitansi) {
      let ff = d.Kwitansi.split('/')
      shell.exec(
        `/opt/libreoffice7.0/program/soffice --convert-to pdf ${d.Kwitansi} --outdir /app/tmp`,
        {
          silent: true,
        }
      )
      line.push('/app/tmp/'+ff[ff.length-1].replace(/(jpg|docx)/,'pdf'))
    } else {
      line.push('')
    }
    if(d.BukuRekening && d.BukuRekening.match(/\.pdf$/i)) {
      bukuRekening.push(d.BukuRekening.replace(/^\//,''))
      line.push(d.BukuRekening)
    } else if(d.BukuRekening) {
      let ff = d.BukuRekening.split('/')
      shell.exec(
        `/opt/libreoffice7.0/program/soffice --convert-to pdf ${d.BukuRekening} --outdir /app/tmp`,
        {
          silent: true,
        }
      )
      line.push('/app/tmp/'+ff[ff.length-1].replace(/(jpg|docx)/,'pdf'))
    } else {
      line.push('')
    }
    csv += line.join(",")+'\r\n'
  }
  
  await Promise.all([
    mergePdf(kwitansi, `tmp/kwitansi_${postfix}.pdf`),
    mergePdf(ktpKades, `tmp/ktp_kades_${postfix}.pdf`),
    // mergePdf(ktpBendahara, 'tmp/ktp_bendahara_${postfix}.pdf'),
    mergePdf(bukuRekening, `tmp/buku_rekening_${postfix}.pdf`),
  ])
  if(kwitansi.length == 1) {
    fs.copyFileSync(kwitansi[0], `tmp/kwitansi_${postfix}.pdf`)
  }
  if(ktpKades.length == 1) {
    fs.copyFileSync(ktpKades[0], `tmp/ktp_kades_${postfix}.pdf`)
  }
  if(bukuRekening.length == 1) {
    fs.copyFileSync(bukuRekening[0], `tmp/buku_rekening_${postfix}.pdf`)
  }
  fs.writeFileSync(`tmp/daftar_berkas_${postfix}.csv`, csv);

  return {
    kwitansi: `tmp/kwitansi_${postfix}.pdf`,
    ktpKades: `tmp/ktp_kades_${postfix}.pdf`,
    bukuRekening: `tmp/buku_rekening_${postfix}.pdf`,
    daftar: `tmp/daftar_berkas_${postfix}.csv`
  }
}

router.get('/berkas-penyaluran', async function(req, res) {
  
  let d = await generatePdfBerkas(req.query)
  
  res.send({
    success: true,
    data: d,
    message: '',
    type: 'object',
  })
  
})

router.post('/berkas-penyaluran', async function(req, res) {
    
  let postfix = moment().format('HHmmss')
  let d = await generatePdfBerkas(req.body)

  const archive = archiver('zip', {
    zlib: {level: 9} // Sets the compression level.
  });
  archive.pipe(fs.createWriteStream(`tmp/berkas-penyaluran_${postfix}.zip`));
  archive.file(d.kwitansi, {name: `kwitansi.pdf`});
  archive.file(d.ktpKades, {name: `ktp_kades.pdf`});
  // archive.file('tmp/ktp_bendahara.pdf', {name: 'ktp_bendahara.pdf'});
  archive.file(d.bukuRekening, {name: `buku_rekening.pdf`});
  archive.file(d.daftar, {name: `daftar_berkas.csv`});
  await archive.finalize();
    
  res.send({
    success: true,
    data: `/get/berkas-penyaluran_${postfix}.zip`,
    message: 'Report Generated',
    type: 'url',
  })
  
})

module.exports = router
