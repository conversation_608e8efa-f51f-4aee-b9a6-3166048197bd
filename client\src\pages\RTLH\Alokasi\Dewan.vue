<template>
  <Page title="Aloka<PERSON>wan">
    <div style="padding: 10px">
      <div style="display: flex">
        <XSelect
          v-model:value="proposal"
          dbref="PRM.SelProposal"
          :value-as-object="true"
          width="95px"
          style="margin-right: 10px"
        />
        <!-- <Checkbox
          v-model:value="hasAllocation"
          style="margin-left: 10px; margin-top: 2px"
          text="Mempunyai Alokasi"
        /> -->
      </div>
      <Grid
        v-model:datagrid="forms.XmlAlokasi"
        dbref="PRM.AlokasiDewan"
        :dbparams="params"
        :filter="filterGrid"
        :disabled="true"
        :autopaging="false"
        height="calc(100vh - 240px)"
        :columns="[
          {
            name: '<PERSON><PERSON>',
            value: 'NamaDewan',
            width: '350px',
          },
          {
            name: '<PERSON><PERSON>',
            value: 'Kuota',
            class: 'plain',
          },
          {
            name: '<PERSON><PERSON><PERSON>',
            value: '<PERSON><PERSON><PERSON>',
            class: 'right',
          },
          {
            name: 'Dapil',
            value: 'AreaAccess',
            class: 'plain',
          },
          {
            name: 'Aks<PERSON>',
            value: 'IsOpen',
            class: 'plain',
          },
        ]"
      >
        <template #row-Kuota="{ row }">
          <XInput v-model:value="row.Kuota" type="number" width="95px" />
        </template>
        <template #row-AreaAccess="{ row }">
          <div center>
            <v-icon @click="ShowAreaAccess(row.UserID)">
              mdi-map-marker-path
            </v-icon>
          </div>
        </template>
        <template #row-IsOpen="{ row }">
          <div center>
            <Checkbox
              v-model:value="row.IsOpen"
              checked-icon="mdi-lock-open-check"
              unchecked-icon="mdi-lock"
            />
          </div>
        </template>
        <template #footer>
          <td>
            <div style="padding: 12px; font-weight: bold">Total</div>
          </td>
          <td>
            <div style="padding: 12px; font-weight: bold; text-align: right">
              {{ $filters.format(totalAlokasi) }}
            </div>
          </td>
          <td>
            <div style="padding: 12px; font-weight: bold; text-align: right">
              {{ $filters.format(totalDipakai) }}
            </div>
          </td>
          <td colspan="2">
            <div style="padding: 12px"></div>
          </td>
        </template>
      </Grid>
      <br />
      <v-btn color="primary" :loading="loading" @click="Save"> SIMPAN </v-btn>
    </div>
    <Modal
      id="modal-roles"
      v-model:show="showAreaAccess"
      title="DAPIL"
      @on-submit="SubmitAreaAccess"
    >
      <AreaAccess v-model:userId="userId" :page-data="areaAccessData" />
    </Modal>
  </Page>
</template>
<script>
import AreaAccess from '@/pages/Admin/User/AreaAccess.vue'
export default {
  components: { AreaAccess },
  data: () => ({
    loading: false,
    proposal: {
      InputName: new Date().getFullYear(),
    },
    forms: {
      XmlAlokasi: [],
    },
    totalAlokasi: 0,
    totalDipakai: 0,
    hasAllocation: false,
    showAreaAccess: false,
    userId: null,
  }),
  computed: {
    params() {
      let { InputName } = this.proposal
      return { Tahun: InputName }
    },
  },
  watch: {
    'forms.XmlAlokasi'(val) {
      this.totalAlokasi = val.reduce((a, b) => a + (b.Kuota || 0), 0)
      this.totalDipakai = val.reduce((a, b) => a + (b.Dipakai || 0), 0)
    },
  },
  created() {
    for (let i = 0; i < 200; i++) {
      this.forms.XmlAlokasi.push({})
    }
  },
  methods: {
    ShowAreaAccess(userId) {
      this.showAreaAccess = true
      this.userId = userId
    },
    async SubmitAreaAccess() {
      let ret = await this.$api.call('Arch.SavUserArea', {
        UserID: this.userId,
        Remarks: this.areaAccessData
          .filter((a) => a.AllowAccess)
          .map((a) => a.AreaID)
          .join('|'),
      })

      if (ret.success) this.showAreaAccess = false
    },
    async Save() {
      this.loading = true
      await this.$api.call('PRM_SavAlokasiDewan', {
        ...this.forms,
        Tahun: this.proposal.InputName,
      })
      this.loading = false
    },
    filterGrid(row) {
      return this.hasAllocation ? Boolean(row.Kuota) : true
    },
  },
}
</script>
<style lang="scss"></style>
