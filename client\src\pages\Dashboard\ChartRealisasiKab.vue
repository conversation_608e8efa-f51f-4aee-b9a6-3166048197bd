<template>
  <div style="height:calc(100vh - 460px);">
    <BarChart
      :chart-data="datacollection"
      :options="chartOptions"
      class="yearly-chart"
    ></BarChart>
  </div>
</template>
<script>
import Bar<PERSON>hart from '../../components/Charts/Bar.vue'
export default {
  components: {
    BarChart,
  },
  data: () => ({
    progress: {},
    datacollection: {},
    chartOptions: {
      title: {
        display: true,
        text: 'REALISASI RTLH PER KABUPATEN',
        fontSize: '16',
      },
      legend: {
        labels: {
          usePointStyle: true,
          pointRadius: 2,
        },
        position: 'bottom',
      },
      responsive: true,
      maintainAspectRatio: false,
    },
  }),
  props: {
    sumber: [Number, String],
  },
  watch: {
    sumber() {
      this.populateProgress()
      this.populateChart()
    },
  },
  created() {
    this.populateProgress()
    this.populateChart()
  },
  methods: {
    async populateProgress() {
      //   let { data } = await this.$api.call('PRM.SelChartProgress', {
      //     TipeData: this.tipeData,
      //   })
      //   this.progress = data[0]
      //   this.progress.TotalIntervensi =
      //     0 +
      //     data[0].APBN +
      //     data[0].APBD1 +
      //     data[0].APBD2 +
      //     data[0].CSR +
      //     data[0].Lain +
      //     data[0].Validasi +
      //     data[0].DiluarPrioritas
      //   this.progress.SisaPBDT = data[0].TotalData - this.progress.TotalIntervensi
    },
    async populateChart() {
      if (this.sumber == 2)
        this.chartOptions.title.text =
          'Realisasi Bantuan RTLH dari dana APBD Prov'
      else
        this.chartOptions.title.text =
          'Realisasi Bantuan RTLH dari Berbagai Sumber Pembiayaan'

      let { data } = await this.$api.call('PRM_SelChartRealisasiKab', {
        Sumber: this.sumber,
      })
      let labels = [
        'BANJARNEGARA',
        'BANYUMAS',
        'BATANG',
        'BLORA',
        'BOYOLALI',
        'BREBES',
        'CILACAP',
        'DEMAK',
        'GROBOGAN',
        'JEPARA',
        'KARANGANYAR',
        'KEBUMEN',
        'KENDAL',
        'KLATEN',
        'KOTA MAGELANG',
        'KOTA PEKALONGAN',
        'KOTA SALATIGA',
        'KOTA SEMARANG',
        'KOTA SURAKARTA',
        'KOTA TEGAL',
        'KUDUS',
        'MAGELANG',
        'PATI',
        'PEKALONGAN',
        'PEMALANG',
        'PURBALINGGA',
        'PURWOREJO',
        'REMBANG',
        'SEMARANG',
        'SRAGEN',
        'SUKOHARJO',
        'TEGAL',
        'TEMANGGUNG',
        'WONOGIRI',
        'WONOSOBO',
      ]
      let colors = {
        '2020': '#CDDC39',
        '2017': '#3F51B5',
        '2018': '#2196F3',
        '2019': '#009688',
      }
      let ds = []
      let total = {}
      data.forEach((d) => {
        let dx = {
          type: 'bar',
          data: [],
          baseColor: colors[d.Tahun],
          pointStyle: 'rectRot',
          pointRadius: 3,
        }
        total[d.Tahun] = 0
        Object.keys(d).forEach((k) => {
          if (k !== 'Tahun') {
            dx.data.push(d[k])
            total[d.Tahun] += d[k]
          }
        })
        dx.label = `${d.Tahun} (${total[d.Tahun].toLocaleString()})`
        ds.push(dx)
      })

      this.datacollection = {
        labels: labels,
        datasets: ds,
      }
    },
    randomScalingFactor() {
      return Math.floor(Math.random() * (50 - 5 + 1)) + 5
    },
  },
}
</script>
<style lang="scss">
.realisasi-chart {
  canvas {
    height: 300px;
  }
}
</style>
