<template>
  <div class="sidebar-validasi">
    <div v-if="tabs.length" style="display: flex; font-size: 12px">
      <div
        v-show="tabs.includes(2)"
        class="tab"
        :class="{
          active: tabId == 2,
        }"
        @click="tabId = 2"
      >
        BANKEU
      </div>
      <div
        v-show="tabs.includes(12)"
        class="tab"
        :class="{
          active: tabId == 12,
        }"
        @click="tabId = 12"
      >
        BANKAB
      </div>
      <div
        v-show="tabs.includes(1)"
        class="tab"
        :class="{
          active: tabId == 1,
        }"
        @click="tabId = 1"
      >
        BSPS
      </div>
      <!-- <div
        class="tab"
        v-show="tabs.includes(9)"
        @click="tabId = 9"
        :class="{
          active: tabId == 9,
        }"
      >
        BSPS-KL
      </div> -->
      <div
        v-show="tabs.includes(4)"
        class="tab"
        :class="{
          active: tabId == 4,
        }"
        @click="tabId = 4"
      >
        CSR
      </div>
      <div
        v-show="tabs.includes(13)"
        class="tab"
        :class="{
          active: tabId == 13,
        }"
        @click="tabId = 13"
      >
        BAZNAS
      </div>
    </div>
    <div style="padding: 10px; display: flex">
      <XSelect
        v-show="!searchMode"
        v-model:value="proposal"
        dbref="PRM.SelProposal"
        :value-as-object="true"
        width="95px"
        style="margin-right: 10px"
        @change="UpdateValue"
      />
      <XSelect
        v-show="!searchMode"
        v-model:value="kabupaten"
        dbref="PRM.SelPBDTCity"
        :dbparams="{}"
        :value-as-object="true"
        width="190px"
        @change="UpdateValue"
      />
      <XInput
        v-show="searchMode"
        v-model:value="keyword"
        type="text"
        placeholder="Cari .."
        width="280px"
      />
      <v-icon
        v-show="!searchMode"
        style="height: 27px; margin-left: 10px"
        @click="searchMode = !searchMode"
        >mdi-magnify</v-icon
      >
      <v-icon
        v-show="searchMode"
        style="height: 27px; margin-left: 10px"
        @click="searchMode = !searchMode"
        >mdi-close</v-icon
      >
    </div>
    <div>
      <div v-show="loading" style="font-size: 14px; text-align: center">
        <v-icon left>mdi-loading mdi-spin</v-icon>
        Loading ...
      </div>
      <List
        v-show="!loading && itemarea.length"
        v-model:items="itemarea"
        :filters="{
          keyword: keyword,
          filter: filterArea,
        }"
        :height="`calc(100vh - ${!tabs.length ? 160 : 200}px)`"
        :select-on-load="true"
        @item-click="AreaClicked"
      >
        <template #default="{ row }">
          <div :class="`ordr-${row.Ordr}`">
            <div
              :style="{
                color: row.KelTipe == 'KEL' ? 'rgb(30, 136, 229)' : '#333',
              }"
            >
              <v-icon
                v-show="row.Ordr == 2"
                style="height: 16px"
                :style="{ color: warnaDesa[row.Priority] || 'palegreen' }"
              >
                mdi-circle-medium
              </v-icon>
              {{ row.KelTipe == 'KEL' ? 'K.' : '' }}
              {{ row.AreaName }}

              <span
                v-if="row.DataTag"
                style="
                  font-size: 10px;
                  background: lightblue;
                  padding: 3px;
                  border-radius: 3px;
                "
              >
                {{ row.DataTag }}
              </span>
            </div>
            <div class="status">
              <div
                v-for="(r, idx) in (row.Rekom || '').split(',')"
                v-show="row.Rekom"
                :key="idx"
                tooltip="'REKOM ' + r"
                class="s-REKOM"
                :class="'s-REKOM' + r"
              >
                {{ r }}
              </div>
              <div
                v-show="row.JmlBansos !== null"
                v-tooltip="'Jml Disetujui/Jml Usulan'"
                class="badge s-jml"
              >
                {{ row.Approved }} / {{ row.JmlBansos }}
              </div>
            </div>
          </div>
        </template>
      </List>
    </div>
  </div>
</template>
<script>
import { mapActions } from 'vuex'
export default {
  props: {
    value: Object,
    dbref: {
      type: String,
      default: 'PRM.SelProposalArea',
    },
    tahun: {
      type: [String, Number],
      default: () => {
        return new Date().getFullYear()
      },
    },
    tabs: {
      type: Array,
      default: () => [2, 12, 1, 9, 4, 13],
    },
    rebind: Number,
  },
  data: () => ({
    kabupaten: { val: null, txt: null },
    proposal: { InputName: new Date().getFullYear() },
    kodeDagri: '',
    selectedArea: {},
    searchMode: false,
    keyword: '',
    itemarea: [],
    tabId: 2,
    loading: false,
    pending: false,
    warnaDesa: ['palegreen', 'red', 'yellow', 'palegreen'],
  }),
  computed: {
    listParams() {
      return {
        Kabupaten: this.kabupaten.txt,
        SumberID: this.tabId,
        ProposalID: this.proposal.ProposalID,
      }
    },
  },
  watch: {
    value(val) {
      if (val.Tahun) this.proposal = { InputName: val.Tahun }
      if (val.KodeDagri && val.KodeDagri != this.kodeDagri)
        this.getByKodeDagri(val.KodeDagri)
      else {
        if (val.Kabupaten) this.kabupaten = { txt: val.Kabupaten }
      }
    },
    searchMode(val) {
      if (!val) this.keyword = ''
    },
    listParams(val) {
      if (!this.pending) {
        this.oldParams = JSON.stringify(val)
        this.$nextTick(() => {
          this.Populate()
        })
      }
    },
    tabId() {
      this.UpdateValue()
    },
  },
  created() {
    this.proposal = {
      InputName: this.tahun,
    }
    this.tabId = this.tabs[0]
  },
  mounted() {
    if (Object.keys(this.$route.query).length) {
      this.area = this.$route.query
    } else if (window.sessionStorage.getItem('side-area')) {
      setTimeout(() => {
        let areaVal = JSON.parse(window.sessionStorage.getItem('side-area'))
        this.$emit('update:value', {
          ...areaVal,
          Tahun: this.proposal.InputName,
          ProposalID: this.proposal.ProposalID,
        })
      }, 500)
    }
  },
  methods: {
    ...mapActions(['setPageFocused']),
    async Populate() {
      if (!this.listParams.Kabupaten) return

      this.loading = true
      let res = await this.$api.call(this.dbref, this.listParams, {
        useCache: true,
      })
      if (res.data) {
        let jmlBansos = 0
        let jmlApproved = 0
        for (let i = res.data.length - 1; i >= 0; i--) {
          let d = res.data[i]
          if (d.Ordr == 1) {
            d.JmlBansos = jmlBansos
            d.Approved = jmlApproved
            jmlBansos = 0
            jmlApproved = 0
          } else {
            jmlBansos += d.JmlBansos
            jmlApproved += d.Approved
          }
        }
        this.itemarea = res.data
      }
      this.loading = false
    },
    AreaClicked(item) {
      if (!item || !item.AreaID) return
      this.pending = true
      setTimeout(() => {
        this.pending = false
      }, 500)

      this.setPageFocused(true)
      let areaVal = {
        tabId: this.tabId,
        Sumber: this.tabId,
        Tahun: this.proposal.InputName,
        ProposalID: this.proposal.ProposalID,
        Kabupaten: this.kabupaten.txt,
        Kecamatan: item.Kecamatan,
        Kelurahan: item.Ordr == 1 ? null : item.AreaName,
        KelurahanID: item.Ordr == 1 ? null : item.AreaID,
        KodeDagri: item.Ordr == 1 ? null : item.KodeDagri,
      }
      window.sessionStorage.setItem('side-area', JSON.stringify(areaVal))
      this.$emit('update:value', areaVal)
    },
    filterArea(item) {
      return item.AreaName.match(new RegExp(this.keyword, 'i'))
    },
    async getByKodeDagri(kodeDagri) {
      if (this.kodeDagri == kodeDagri) return
      this.kodeDagri = kodeDagri
      let res = await this.$api.call('Arch.SelFullArea', {
        KodeDagri: kodeDagri,
      })
      if (res.success) {
        let item = res.data[0]
        let areaVal = {
          ...item,
          tabId: this.tabId,
          Sumber: this.tabId,
          Tahun: this.proposal.InputName,
          ProposalID: this.proposal.ProposalID,
          KodeDagri: kodeDagri,
        }
        window.sessionStorage.setItem('side-area', JSON.stringify(areaVal))
        this.$emit('update:value', areaVal)
      }
    },
    UpdateValue() {
      this.$emit('update:value', {
        tabId: this.tabId,
        Sumber: this.tabId,
        Tahun: this.proposal.InputName,
        ProposalID: this.proposal.ProposalID,
        Kabupaten: this.kabupaten.txt,
      })
    },
  },
}
</script>
<style lang="scss">
.sidebar-validasi {
  background: white;
  .tab {
    text-align: center;
    flex: 1;
    font-size: 13px;
    padding: 5px 10px;
    background: #ddd;
    color: gray;
    border-right: 0.5px solid silver;
    cursor: pointer;

    &.active {
      background: white;
      font-weight: bold;
      border: 1px solid silver;
      border-bottom: transparent;
    }
  }
}
</style>
